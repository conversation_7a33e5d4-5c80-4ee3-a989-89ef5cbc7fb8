package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_code_dev_sanitizers {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
    ]
    static Map code_settings = [
        deploy_tests                 : true,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        poolbuild_data               : true,
        skip_frosty_trigger          : true,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [:]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        server_asset                    : 'Game/Setup/Build/DevMPLevels',
        asset                           : 'DevLevels',
        extra_data_args                 : ['--pipeline-args -clean --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args               : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        enable_lkg_p4_counters          : true,
        separate_symbol_store_upload    : false,
        skip_icepick_settings_file      : true,
        statebuild_code                 : false,
        strip_symbols                   : false,
        timeout_hours_data              : 8,
    ]

    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]

    static Map preflight_settings = [:]

    static List code_matrix = [
        [name: 'tool', configs: [[name: 'release', tool_targets: ['pipeline']],]], // used by autotests cooking data
        // asan jobs with tests
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, asan_enabled: true, custom_tag: 'asan', tool_targets: ['pipeline', 'win64-dll']],]], // used by unittests and data builds
        [name: 'ps5', configs: [[name: 'final', compile_unit_tests: true, asan_enabled: true, custom_tag: 'asan'],]],
        [name: 'xbsx', configs: [[name: 'final', compile_unit_tests: true, asan_enabled: true, custom_tag: 'asan'],]],
        // asan jobs without tests
        [name: 'win64game', configs: [[name: 'final', compile_unit_tests: true, asan_enabled: true, custom_tag: 'asan'],]],
        [name: 'win64server', configs: [[name: 'final', asan_enabled: true, custom_tag: 'asan'],]],
        [name: 'linux64server', configs: [[name: 'final', asan_enabled: true, custom_tag: 'asan'],]],
        // ubsan jobs with tests
        [name: 'ps5', configs: [[name: 'final', compile_unit_tests: true, ubsan_enabled: true, custom_tag: 'ubsan'],]],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.bilbo.register-bfdata-dronebuild', args: ['code_changelist', 'mirror_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.ch1_asan_consoles.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.ch1_ubsan_consoles.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.ch1_asan_windows.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.unittests_asan_static.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.unittests_asan_tool.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/job/ch1-code-dev-sanitizers.autotest.unittests_ubsan_static.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'server', custom_tag: 'asan'],
        [name: 'win64', custom_tag: 'asan'],
        [name: 'xbsx', custom_tag: 'asan'],
        [name: 'ps5', custom_tag: 'asan'],
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false, custom_tag: 'asan'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
