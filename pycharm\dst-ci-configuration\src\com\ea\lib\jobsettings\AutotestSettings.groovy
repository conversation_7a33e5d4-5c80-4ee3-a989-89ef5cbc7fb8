package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps
import com.ea.lib.jobs.LibAutotest
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.jobs.AutotestModel
import com.ea.matrixfiles.AutotestMatrix
import groovy.json.JsonBuilder

class AutotestSettings extends JobSetting {

    boolean enableP4Counters
    boolean setIntegrationInfo
    boolean isTestWithLooseFiles
    boolean isFrostedAutotest
    boolean needGameServer
    boolean installPypiwin32
    boolean useExistingFilerBuild
    String dataset
    String autotestMatrixName
    String testCategoryName
    // autotestRun
    String serverPlatform
    Map fbLoginDetails
    String testDefinition
    String reportingBranch
    String customTag
    String testSuitesJsonFile = 'testSuites.json'
    String testSuitesString = ''

    void initializeAutotestStart(def branchFile, def masterFile, def projectFile, String branchName, AutotestCategory testCategory) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        List modifiers = ['autotest']
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)

        // only when branch and test category both enable p4 counter and we are showing tests results
        //	set enable_lkg_p4_counters=true
        def enableLkgP4Counters = branchInfo.enable_lkg_p4_counters ?: false
        def enableAutotestP4Counters = testCategory.enableP4Counters
        def showTestResults = testCategory.testInfo.showTestResultsOnJenkins != null ?
            testCategory.testInfo.showTestResultsOnJenkins : testCategory.showTestResultsOnJenkins
        enableP4Counters = enableLkgP4Counters && enableAutotestP4Counters && showTestResults

        setIntegrationInfo = (branchInfo.set_integration_info != null && testCategory.name == branchInfo.set_integration_info.test_category) ?: false
        isTestWithLooseFiles = testCategory.isTestWithLooseFiles
        needGameServer = testCategory.needGameServer
        projectName = projectFile.name
        dataset = branchInfo.dataset
        autotestMatrixName = projectFile.autotest_matrix
        testCategoryName = testCategory.name
    }

    void initializeAutotestManualStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        description = 'Manual trigger start job for Autotest jobs.'
        dataset = branchInfo.dataset
        projectName = projectFile.name
        autotestMatrixName = projectFile.autotest_matrix
    }

    void initializeAutotestRun(def branchFile, def masterFile, def projectFile, String branchName, AutotestModel autotestModel, AutotestCategory testCategory) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        def modifiers = ['autotest']
        String platform = autotestModel.platform
        userCredentials = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'user_credentials', null, projectFile)
        boolean uploadJournals = testCategory.uploadJournals
        boolean captureVideo = testCategory.captureVideo
        String packageType = testCategory.format
        boolean runBilbo = testCategory.runBilbo
        testDefinition = testCategory.testDefinition
        dataset = branchInfo.dataset
        codeBranch = branchInfo.code_branch
        dataBranch = branchInfo.data_branch
        reportingBranch = branchInfo.reporting_branch ?: branchInfo.data_branch
        String secretContext = branchInfo.azure_fileshare?.secret_context
        String targetBuildShare = branchInfo.azure_fileshare?.target_build_share
        List<String> additionalToolsToInclude = branchInfo.azure_fileshare?.additional_tools_to_include

        String frostingEndpointOverride = testCategory.frostingEndpointOverride
        String serverPlatform = testCategory.testInfo.serverPlatform ?: testCategory.serverPlatform
        String serverAsset = testCategory.testInfo.serverAsset ?: testCategory.serverAsset
        boolean showTestResults = testCategory.testInfo.showTestResultsOnJenkins != null ?
            testCategory.testInfo.showTestResultsOnJenkins : testCategory.showTestResultsOnJenkins
        isTestWithLooseFiles = testCategory.isTestWithLooseFiles
        String testGroup = testCategory.testInfo.testGroup ?: testCategory.name
        String config = testCategory.testInfo.config ?: testCategory.config
        String serverConfig = testCategory.testInfo.serverConfig ?: testCategory.serverConfig
        String elipyScript = testCategory.testInfo.elipyCmd ?: testCategory.elipyCmd
        boolean cleanMasterVersionCheck = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'clean_master_version_check', false, projectFile)
        boolean fetchTests = false
        String customTestSuiteData = testCategory.testInfo.customTestSuiteData ?: testCategory.customTestSuiteData
        String region = autotestModel.region ?: testCategory.testInfo.region ?: testCategory.region
        String serverRegion = testCategory.testInfo.serverRegion ?: testCategory.serverRegion
        String extraFrameworkArgs = testCategory.testInfo.extraFrameworkArgs ?: testCategory.extraFrameworkArgs ?: branchInfo.icepick_extra_framework_args
        String forcedIcepickSettingsFiles = testCategory.testInfo.forceIcepickSettingsFiles ?: testCategory.forceIcepickSettingsFiles
        Boolean testInfoUseShiftBuild = testCategory.testInfo.useShiftBuild
        boolean useShiftBuild = testInfoUseShiftBuild != null ? testInfoUseShiftBuild : testCategory.useShiftBuild
        Boolean testInfoUseSpinBuild = testCategory.testInfo.useSpinBuild
        boolean useSpinBuild = testInfoUseSpinBuild != null ? testInfoUseSpinBuild : testCategory.useSpinBuild
        Integer numTestRuns = testCategory.testInfo.numTestRuns
        String testInfoPoolType = testCategory.testInfo.poolType

        Boolean testInfoEnableSyncFiles = testCategory.testInfo.enableSyncFiles
        boolean enableSyncFiles = testInfoEnableSyncFiles != null ? testInfoEnableSyncFiles : testCategory.enableSyncFiles

        String frostbiteLicensee = branchInfo.frostbite_licensee
        extraArgs = branchInfo.extra_icepick_args ?: ''
        boolean importAvalanche = branchInfo.import_avalanche_autotest
        boolean cadetActivateToolset = branchInfo.cadet_activate_toolset ?: false

        needGameServer = testCategory.needGameServer
        String buildType = testCategory.buildType
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        String defaultJobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        String priorityJobLabel = ''
        String poolbuildLabel = LibCommonNonCps.get_setting_value(branchInfo, [], 'poolbuild_label', 'poolbuild', projectFile)
        // Check for job_label_poolbuild in master settings
        String job_label_poolbuild = LibCommonNonCps.get_setting_value(branchInfo, [], 'job_label_poolbuild', null, projectFile)
        isFrostedAutotest = testCategory.isFrostedAutotest
        isTestWithLooseFiles = testCategory.isTestWithLooseFiles
        useExistingFilerBuild = testCategory.useExistingFilerBuild
        customTag = testCategory.customTag
        installPypiwin32 = testCategory.testInfo.installPypiwin32 ?: testCategory.installPypiwin32
        boolean cookData = !isTestWithLooseFiles
        workspaceRoot = isFrostedAutotest ? branchInfo.azure_workspace_root : branchInfo.workspace_root
        testSuitesJsonFile = [workspaceRoot, testSuitesJsonFile].join('\\')
        elipyCall = isFrostedAutotest ? branchInfo.azure_elipy_call : branchInfo.elipy_call
        elipyInstallCall = isFrostedAutotest ? branchInfo.azure_elipy_install_call : branchInfo.elipy_install_call
        if (branchInfo.poolbuild_autotest) {
            // Use job_label_poolbuild if defined, otherwise use poolbuild_label
            defaultJobLabel = job_label_poolbuild ?: "${poolbuildLabel}"
            defaultJobLabel += " && ${platform}"
        }
        if (testCategory.testInfo.hasPinnedAgents) {
            priorityJobLabel = "${testCategory.name} && ${platform}"
        }
        jobLabel = priorityJobLabel ?: testCategory.testInfo.jobLabel ?: testCategory.jobLabel ?: defaultJobLabel
        def jobRemoteLabel = testCategory.testInfo.remoteLabel ?: testCategory.remoteLabel
        if (jobRemoteLabel) {
            jobLabel = jobLabel.replaceAll(/statebuild\w*/, 'statebuild_' + jobRemoteLabel)
            jobLabel = jobLabel.replaceAll(/poolbuild\w*/, 'poolbuild_' + jobRemoteLabel)
        } else if (!jobRemoteLabel && (jobLabel.contains('statebuild_') || jobLabel.contains('poolbuild_'))) {
            jobLabel = jobLabel.replaceAll(/statebuild_\w+/, 'statebuild')
            jobLabel = jobLabel.replaceAll(/poolbuild_\w+/, 'poolbuild')
        }
        if (isFrostedAutotest) {
            jobLabel = "${this.branchName} && frostedtests && cloud"
        }
        Boolean installFbPrerequisites = isFrostedAutotest
        String extraNodeLabel = branchInfo.autotest_remote_settings?.get(jobRemoteLabel)?.extra_node_label ?: ''
        if (extraNodeLabel && !jobLabel.contains(extraNodeLabel)) {
            jobLabel = jobLabel.replace(platform, platform + '_' + extraNodeLabel)
        }
        def timeoutHours = AutotestMatrix.getTimeoutHours(testCategory)
        timeoutMinutes = timeoutHours * 60
        List<Map> testSuites = []
        autotestModel.tests.each { testSuite ->
            // codenarc-disable UnnecessaryCollectCall
            Map testSuiteMap = [name: testSuite.name, extraArgs: testSuite.extraArgs.collect { it.toString() }]
            needGameServer = needGameServer || testSuite.needGameServer

            if (!fetchTests && testSuite.fetchTests) {
                fetchTests = testSuite.fetchTests
            }

            String poolType = 'atf'
            if (testInfoPoolType != null) {
                poolType = testInfoPoolType
            }
            if (testSuite.poolType != null) {
                poolType = testSuite.poolType
            }

            jobLabel = testSuite.jobLabel ?: jobLabel

            if (testSuite.targetsWindows) {
                testSuiteMap.extraArgs += ['--default-targets-windows', testSuite.targetsWindows]
            } else if (poolType) {
                testSuiteMap.extraArgs += ['--pool-type', poolType]
            }

            if (LibAutotest.checkGameServer(testSuite, testCategory)) {
                // To maximize utilization of the DICE QE test farm hardware resources we
                // have an ATF pool that is preferred to be used for running server-only
                // instances of the game.
                testSuiteMap.extraArgs += ['--default-server-target-group-overrides', 'dedicated_server_pool']
            }
            testSuites.add(testSuiteMap)
        }
        testSuitesString = new JsonBuilder(testSuites).toString()

        if (branchInfo.statebuild_icepick == false || branchInfo.statebuild_autotest == false) {
            importAvalanche = false
            jobLabel = branchInfo.data_branch
        }

        // Avoid triggering Icepick retry mechanism in case of particular "infrastructure failures" that we rarely seem to recover from anyway
        extraArgs += ' --retry-infrastructure-failure 0 --ps5-retry-launch-on-fail false'

        // Increase test stability on ps5 farm kits by increasing wait time on processes, value recommended by sony

        extraArgs += ' --wait-for-output-ms 120000'

        if (projectFile.single_perforce_server) {
            extraArgs += branchInfo.report_build_version ?: ' --reporting-build-version-id %code_changelist%'
        } else {
            extraArgs += branchInfo.report_build_version ?: ' --reporting-build-version-id %data_changelist%'
        }

        if (fetchTests) {
            extraArgs += " --ft ${fetchTests}"
        }

        if (testCategory.isFrostedAutotest) {
            extraArgs += " --if ${testCategory.isFrostedAutotest} --tbs ${targetBuildShare} --sctx ${secretContext}"
            buildType = 'dll'
            config = 'release'
            if (cadetActivateToolset) {
                extraArgs += ' --cat'
            }
            fbLoginDetails = branchInfo.cadet_p4_fb_settings ?: fbLoginDetails
            if (fbLoginDetails) {
                extraArgs += ' --fpu %fb_p4_user% --fpp "%fb_p4_passwd%"'
                extraArgs += " --fbp ${fbLoginDetails.p4_port}"
            }
        }
        if (additionalToolsToInclude) {
            extraArgs += additionalToolsToInclude.collect { " --atti $it" }.join()
        }
        if (customTestSuiteData) {
            extraArgs += " --ctsd ${customTestSuiteData}"
        }
        if (cookData) {
            extraArgs += " --ic ${cookData}"
        }
        if (importAvalanche) {
            extraArgs += " --ias ${importAvalanche}"
        }
        if (needGameServer) {
            extraArgs += " --ngs ${needGameServer}"
            extraArgs += ' --sbi %server_build_id%'
        }
        if (uploadJournals) {
            extraArgs += ' --frosting-upload-trace-journal true'
        }
        if (captureVideo) {
            if (platform == Name.WIN64.name()) {
                extraArgs += ' --runtime-debug-args="-Vision.Enabled true -Vision.StreamOnBoot true -Vision.SpreadEnabled true"'
            } else {
                extraArgs += ' --runtime-debug-args="-Vision.Enabled true -Vision.StreamOnBoot true"'
            }
        }
        if (frostbiteLicensee) {
            extraArgs += " -l ${frostbiteLicensee}"
        }
        if (serverPlatform) {
            extraArgs += " --sp ${serverPlatform}"
        }
        if (serverAsset) {
            extraArgs += " --sas ${serverAsset}"
        }
        if (config) {
            extraArgs += " -c ${config}"
        }
        if (serverConfig) {
            extraArgs += " --sc ${serverConfig}"
        }
        if (userCredentials) {
            extraArgs += ' -e %monkey_email% -p "%monkey_passwd%"'
        }
        if (buildType) {
            extraArgs += " --bt ${buildType}"
        }
        if (useShiftBuild) {
            extraArgs += ' --shift-changelist %code_changelist%_%data_changelist%'
        }
        if (useSpinBuild) {
            extraArgs += " --build-version-tags data_branch:$dataBranch;data_cl:%data_changelist%;code_branch:$codeBranch;code_cl:%code_changelist%"
        }
        if (runBilbo) {
            extraArgs += ' --rb true'
        }
        if (testGroup) {
            extraArgs += " --tg ${testGroup.trim()}"
        }
        if (extraFrameworkArgs) {
            extraArgs += " --efa \"${extraFrameworkArgs}\""
        }
        if (isTestWithLooseFiles && packageType && region) {
            extraArgs += " --pt ${packageType} -r ${region}"

            // Temp deployment has been shown to mitigate some robocopy-related issues
            // we have seen by first making a local copy of the loose file build before
            // deploying it to test target, at the cost of increased test running time.
            extraArgs += " --use-temp-deployment ${testCategory.useTempDeploymentIfLooseFiles}"

            extraArgs += " --copy-build-to-share-timeout-seconds ${testCategory.copyLooseFileBuildToWindowsShareTimeoutSeconds}"

            // https://electronic-arts.slack.com/archives/C013ASSHCAJ/p1655913494371149?thread_ts=1655384906.163119&cid=C013ASSHCAJ
            extraArgs += ' --superbundles true'
        }
        if (!isTestWithLooseFiles) {
            extraArgs += ' --clean %clean%'
        }
        if (serverRegion) {
            extraArgs += " --sr ${serverRegion}"
        }
        if (frostingEndpointOverride) {
            extraArgs += " --frosting-api-address ${frostingEndpointOverride}"
        }
        if (showTestResults) {
            extraArgs += " --str ${showTestResults}"
        }
        if (enableSyncFiles) {
            extraArgs += " --sfs ${enableSyncFiles}"
        }
        if (forcedIcepickSettingsFiles) {
            extraArgs += " --sf ${forcedIcepickSettingsFiles}"
        } else if (!branchInfo.skip_icepick_settings_file) {
            extraArgs += " --sf ${branchInfo.icepick_settings_files ?: projectFile?.icepick_settings?.settings_files ?: 'Config/Icepick/IcepickSettings.ini'}"
        }
        if (cleanMasterVersionCheck) {
            extraArgs += " --cmvc ${cleanMasterVersionCheck}"
        }

        if (useExistingFilerBuild) {
            extraArgs += ' --use-existing-filer-build true'
        }
        if (customTag) {
            extraArgs += " --custom-tag ${customTag}"
        }
        if (numTestRuns) {
            extraArgs += " --num-test-runs ${numTestRuns}"
        }
        if (installFbPrerequisites) {
            extraArgs += ' --ifbp true'
        }

        elipyCmd = "$elipyCall $elipyScript $platform" +
            " --tsj $testSuitesJsonFile" +
            " --cb $codeBranch --cc %code_changelist%" +
            " --db $dataBranch --dc %data_changelist%" +
            " --cbi %client_build_id% --reporting-branch $reportingBranch" +
            ' --reporting-public-suite true --reporting-is-monkey true --no-key-press true' +
            " --td %test_definition% --dd $dataset $extraArgs"
    }
}
