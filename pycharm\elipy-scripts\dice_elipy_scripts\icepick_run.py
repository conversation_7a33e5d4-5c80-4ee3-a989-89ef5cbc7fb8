# pylint: disable=line-too-long
"""
icepick_run.py

DOCUMENATION:
 - Flow Overview: https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview
 - Jenkins Config: https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview
"""
# pylint: enable=line-too-long
import os
import psutil
import click
import time
import json
import getpass
from dice_elipy_scripts.utils import autotest_utils
from dice_elipy_scripts.utils import frosty_build_utils
from dice_elipy_scripts.utils.azure_filer_utils import authenticate_filer
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from dice_elipy_scripts.utils.icepick_utils import (
    icepick_clean,
    sync_specified_files_to_head,
    save_icepick_logs,
)
from elipy2.exceptions import ConfigValueNotFoundException
from elipy2 import (
    avalanche,
    filer,
    filer_paths,
    data,
    frostbite_core,
    local_paths,
    LOGGER,
    SETTINGS,
    build_metadata_utils,
)
from elipy2 import running_processes
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.enums_utils import CookTypes, string_to_enum
from elipy2.frostbite import fbcli, fbenv_layer, icepick
from elipy2.telemetry import collect_metrics

from typing import Dict, Optional


# pylint: disable=too-many-locals
@click.command(
    "icepick_run",
    short_help="Run icepick test.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.argument("platform", required=True)
@click.argument("run-args", nargs=-1, type=click.UNPROCESSED)
@click.option(
    "--test-suites",
    "--ts",
    help="The test suite to run followed by pool-type. Multiple.",
    multiple=True,
    required=True,
)
@click.option(
    "--settings-files",
    "--sf",
    help="Settings files relative to the data folder.",
    multiple=True,
)
@click.option(
    "--assets",
    "--as",
    default=None,
    help="Assets to build before running icepick.",
    multiple=True,
)
@click.option(
    "--asset",
    "-a",
    default=["preflightlevels"],
    help="Deprecated parameter. Use --assets instead.",
    multiple=True,
)
@click.option(
    "--code-branch", "--cb", default=None, required=True, help="Branch to fetch code from."
)
@click.option(
    "--code-changelist",
    "--cc",
    default=None,
    required=True,
    help="Which code changelist to use.",
)
@click.option(
    "--data-changelist",
    "--dc",
    default=None,
    help="Which data changelist to use.",
    required=True,
)
@click.option(
    "--client-build-id",
    "--cbi",
    default="None",
    help="Which client build to use.",
)
@click.option(
    "--server-build-id",
    "--sbi",
    default=None,
    help="Which server build to use.",
)
@click.option("--data-branch", "--db", help="Branch to fetch Avalanche state from.", default="")
@click.option(
    "--datadir",
    "--dd",
    default="data",
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option(
    "--frosting-report",
    "--fr",
    default=True,
    help="Toggle to send frosting report.",
)
@click.option("--lease", "--le", default=False, help="Lease, run remotely.")
@click.option(
    "--build-type",
    "--bt",
    default="static",
    type=click.Choice(["static", "dll", None]),
    help="Static",
)
@click.option("--autobuild", "--ab", default=True, help="Autobuild")
@click.option("--config", "-c", default="final", help="Config")
@click.option(
    "--import-avalanche-state", "--ias", default=False, help="Imports Avalanche state from filer."
)
@click.option("--icepick-cook", "--ic", default=False, help="Cook data in icepick.")
@click.option("--test-definition", "--td", help="Which test to register.", default="test_def")
@click.option(
    "--need-game-server",
    "--ngs",
    default=False,
    help="Set this if the test requires a game server build.",
)
@click.option("--region", "-r", help="Which region to deploy for (default is ww).", default="ww")
@click.option(
    "--server-region",
    "--sr",
    help="Which server region to deploy for (default is ww).",
    default="ww",
)
@click.option(
    "--server-config",
    "--sc",
    help="Server config (default is config value).",
    default=None,
)
@click.option("--package-type", "--pt", help="Which package type to test.", default=None)
@click.option(
    "--server-assets",
    "--sas",
    help="what server asset to build if needed.",
    default=None,
    multiple=True,
)
@click.option(
    "--fetch-frosted", "--ff", default=False, help="Deprecated parameter. Use --is-frosted instead."
)
@click.option(
    "--is-frosted",
    "--if",
    default=False,
    help="Flag to determine if it is a FrostEd job run.",
)
@click.option("--licensee", "-l", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--password",
    "-p",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", "-e", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    "--du",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option(
    "--show-test-results",
    "--str",
    default=False,
    help="Report test failures on jenkins",
)
@click.option(
    "--run-bilbo",
    "--rb",
    default=False,
    help="Whether or not to report test results to the configured metadata services",
)
@click.option(
    "--test-group",
    "--tg",
    default="nogroup",
    help="Set test group name as part of metadata.",
)
@click.option("--inert-run", "--ir", default=False, help="Run but don't do anything")
@click.option(
    "--extra-framework-args",
    "--efa",
    default=None,
    help="Extra arguments for Icepick to pass to any Framework commands it starts",
)
@click.option(
    "--server-platform",
    "--sp",
    default="server",
    help="Which server platform to use, can be either server (windows) or linuxserver.",
)
@click.option(
    "--use-shift-build",
    "--usb",
    default=False,
    type=bool,
    help="Should these tests use a Shift build or not",
)
@click.option(
    "--use-spin-build",
    "--spin",
    default=False,
    type=bool,
    help="Should these tests use a Spin build or not",
)
@click.option(
    "--fetch-tests",
    "--ft",
    default=False,
    help="Fetch test files using Tests_Win64-Dll_release_Files.txt",
)
@click.option(
    "--code-files",
    "--cf",
    default=None,
    help="What code files to sync to head",
    multiple=True,
)
@click.option(
    "--code-p4-port",
    "--cpp",
    default="dice-p4buildedge02-fb.dice.ad.ea.com:2001",
    help="Code P4 to sync files from",
)
@click.option(
    "--data-files",
    "--df",
    default=None,
    help="What data files to sync to head",
    multiple=True,
)
@click.option(
    "--data-p4-port",
    "--dpp",
    default="p4-tunguska-build01.dice.ad.ea.com:2001",
    help="Data P4 to sync files from",
)
@click.option(
    "--p4-user",
    "--pu",
    default=f"dice\\{getpass.getuser()}",
    help="What users should be used to connect to P4",
)
@click.option(
    "--sync-files", "--sfs", default=False, help="Should sync the specified files to head"
)
@click.option("--clean", type=click.BOOL, default=False, help="Perform a clean for this test")
@click.option(
    "--custom-test-suite-data",
    "--ctsd",
    default="",
    help="Custom test suite metadata to pass to Icepick.",
)
@click.option(
    "--clean-master-version-check",
    "--cmvc",
    default=False,
    help="Run clean on master version update.",
)
@click.option(
    "--cook-type",
    "--ct",
    type=click.Choice([t.name.lower() for t in CookTypes]),
    default=CookTypes.COOK.name.lower(),
    help="decide which method is called to cook data. [cook, icepick_cook, no_cook].",
)
@click.option(
    "--db-name-prefix",
    "--dnp",
    help="prefix for db name when specifying cook type as AVALANCHE_STORAGE",
)
@click.option(
    "--custom-tag",
    "--cut",
    default=None,
    help="Extra folder before changelist to fetch code from.",
)
@click.option(
    "--pipeline-custom-tag",
    "--pcut",
    default=None,
    help="Extra folder before changelist to fetch pipeline code from.",
)
@click.option(
    "--filer-user",
    "--fu",
    default=None,
    help="username for creating a filer connection",
)
@click.option(
    "--filer-password",
    "--fp",
    default=None,
    help="password for creating a filer connection",
)
@click.option(
    "--enable-hailstorm",
    "--eh",
    type=bool,
    default=True,
    help="Should Icepick cook use the hailstorm server or not",
)
@click.option(
    "--target-build-share",
    "--tbs",
    help="Elipy config key to find buildshare in alternate_build_shares.",
    default=None,
)
@click.option(
    "--secret-context",
    "--sctx",
    help="Elipy config secrets 'where' key for filer auth",
    default=None,
)
@click.option(
    "--additional-tools-to-include",
    "--atti",
    help="Additional tool(s) to pull from network share. Applied when --is-frosted is true.",
    default=(),
    multiple=True,
    type=str,
)
@click.option(
    "--cadet-activate-toolset",
    "--cat",
    is_flag=True,
    help="Install cadet and run fb cadet activate-toolset",
)
@click.option(
    "--fb-p4-port",
    "--fbp",
    default=None,
    help="Fb p4 port",
)
@click.option(
    "--fb-p4-user",
    "--fpu",
    default=None,
    help="Fb p4 user",
)
@click.option(
    "--fb-p4-password",
    "--fpp",
    default=None,
    help="Fb p4 password",
)
@click.option(
    "--cadet-p4-port",
    "--cap",
    default="dice-p4-one.dice.ad.ea.com:2001",
    help="Cadet p4 port, this has to be a commit server.",
)
@click.option(
    "--num-test-runs",
    default="1",
    help="Number of times the test needs to pass to be considered successful.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    run_args,
    test_suites,
    settings_files,
    assets,
    asset,
    code_branch,
    code_changelist,
    cook_type,
    data_changelist,
    db_name_prefix,
    client_build_id,
    server_build_id,
    datadir,
    frosting_report,
    lease,
    build_type,
    autobuild,
    config,
    import_avalanche_state,
    data_branch,
    icepick_cook,
    test_definition,
    need_game_server,
    region,
    server_region,
    server_config,
    package_type,
    server_assets,
    fetch_frosted,
    is_frosted,
    licensee,
    password,
    email,
    domain_user,
    show_test_results,
    run_bilbo,
    test_group,
    inert_run,
    extra_framework_args,
    server_platform,
    use_shift_build,
    use_spin_build,
    fetch_tests,
    code_files,
    code_p4_port,
    data_files,
    data_p4_port,
    p4_user,
    sync_files,
    clean,
    custom_test_suite_data,
    clean_master_version_check,
    custom_tag,
    pipeline_custom_tag,
    filer_user,
    filer_password,
    enable_hailstorm,
    target_build_share,
    secret_context,
    additional_tools_to_include,
    cadet_activate_toolset,
    fb_p4_port,
    fb_p4_user,
    fb_p4_password,
    cadet_p4_port,
    num_test_runs,
):
    """
    Run icepick test.
    """
    if inert_run:
        LOGGER.info("Inert run, sleep for 3 seconds and exit...")
        time.sleep(3)
        return

    if fetch_frosted:
        LOGGER.warning("--fetch-frosted is deprecated. Use --is-frosted instead.")
        is_frosted = True  # Use is_frosted instead of returning early

    metadata_manager = build_metadata_utils.setup_metadata_manager()

    icepick_run_args = list(run_args)
    shift_cl_key = "--shift-changelist"
    shift_cl_in_suite = False
    shift_cl_in_run_args = shift_cl_key in icepick_run_args
    use_shift_build = use_shift_build or shift_cl_in_run_args or shift_cl_in_suite

    build_version_tags_flag = "--build-version-tags"
    build_version_tags_present = build_version_tags_flag in icepick_run_args
    use_spin_build = use_spin_build or build_version_tags_present

    LOGGER.info("Client and Server Id: \n{}\n{}".format(client_build_id, server_build_id))
    client_build = []
    if client_build_id.lower() == "none":
        client_build_id = None
    client_build_id = client_build_id.split(",") if client_build_id else []
    for cbi in client_build_id:
        # if "linux64" not in cbi:
        _client_build = get_build_from_bilbo(
            cbi.lower(), use_shift_build, use_spin_build, is_frosted
        )
        client_build.append(_client_build)
    LOGGER.info("Client Build: \n{}".format(client_build))
    _sdk_platform = ["ps4", "xb1", "ps5", "xbsx"]
    sdk_platform = [
        platform
        for platform in _sdk_platform
        if any(item.get("platform") == platform for item in client_build)
    ]
    LOGGER.info("SDK Platforms to install: {}\n".format(sdk_platform))
    LOGGER.debug(
        "region: {} server_region: {} package_type: {}".format(region, server_region, package_type)
    )
    # Use deprecated param if necessary
    client_assets = assets if assets else list(asset)
    if server_assets:
        server_assets = list(server_assets)

    server_build = None
    server_is_needed = server_build_id and (need_game_server or server_assets)
    if server_is_needed:
        server_build = get_build_from_bilbo(
            server_build_id.lower(), use_shift_build, use_spin_build, is_frosted
        )

    server_config = server_config if server_config else config

    # checking for any running maintenance jobs before continuing
    avalanche.avalanche_blocker_check()

    test_suite_names = autotest_utils.get_test_suites_names(test_suites)

    # adding sentry tags
    add_sentry_tags(__file__, "autotest")

    # ensure the licensee is set
    core_licensee_id = frostbite_core.get_licensee_id()
    frostbite_licensee = [core_licensee_id]
    if licensee:
        frostbite_licensee = list(licensee)

    running_processes.kill()

    set_licensee(frostbite_licensee, list())

    if sync_files:
        sync_specified_files_to_head(
            p4_user, code_p4_port, code_files, data_p4_port, data_files, datadir
        )

    _filer = filer.FilerUtils()

    # authenticate filer if user/password specified
    if filer_user and filer_password:
        _filer.delete_network_connection()
        _filer.auth_network_connection(
            network_path=filer_paths.get_build_share_path(),
            username=filer_user,
            password=filer_password,
        )

    icepicker = icepick.IcepickUtils()
    # clean machine ready for icepick
    icepick_clean(icepicker)

    if password is not None and email is not None:
        frosty_build_utils.authenticate_eapm_credstore(
            user=email, password=password, domain_user=domain_user
        )

    icepick_settings_files_list = list(settings_files)
    icepick_settings_files_list = icepick.IcepickUtils.settings_files_relative_to_absolute_paths(
        icepick_settings_files_list
    )

    LOGGER.info("Using Icepick settings files: {}".format("".join(icepick_settings_files_list)))

    client_builder = data.DataUtils(platform, client_assets, monkey_build_label=data_changelist)
    client_builder.set_datadir(datadir)
    client_import_args = []
    client_extra_args = []

    server_builder = data.DataUtils(server_platform, server_assets, data_changelist)
    server_builder.set_datadir(datadir)
    server_extra_args = []

    # Restart Avalanche service if necessary for non-Shift builds
    if not use_shift_build:
        avalanche.restart_avalanche()

    # Skip SDK installation when using Spin build
    if not use_spin_build:
        frosty_build_utils.install_required_sdks(
            password=password,
            user=email,
            domain_user=domain_user,
            platform=platform.lower(),
            use_shift_build=use_shift_build,
        )
        for _platform in sdk_platform:
            LOGGER.info("Installing SDK for platform: {} ... ".format(_platform))
            frosty_build_utils.install_required_sdks(
                password=password,
                user=email,
                domain_user=domain_user,
                platform=_platform.lower(),
                use_shift_build=use_shift_build,
            )
    else:
        LOGGER.info("Skipping SDK installation for Spin build.")

    if frostbite_core.minimum_fb_version(
        year=2019, version_nr=5, is_pr=True
    ) and "casablanca" not in map(str.lower, frostbite_licensee):
        autobuild = False

    # Fetch Binaries
    cook_type_enum = string_to_enum(CookTypes, cook_type)
    if icepick_cook:
        cook_type_enum = CookTypes.ICEPICK_COOK

    # logics to use fb pullbuild
    if use_shift_build:
        LOGGER.info("Using a Shift build for all tests")
        LOGGER.warning("Setting icepick_cook to False since we are using a Shift build")
        icepick_cook = False
        if shift_cl_in_run_args and shift_cl_in_suite:
            index = icepick_run_args.index(shift_cl_key)
            LOGGER.warning("Removing extra {shift_cl_key} values from icepick_run_args")
            del icepick_run_args[index]  # remove the argument name
            del icepick_run_args[index]  # remove the argument value
        elif not (shift_cl_in_run_args or shift_cl_in_suite):
            icepick_run_args.extend([shift_cl_key, f"{code_changelist}_{data_changelist}"])
    elif not is_frosted and "frosty" in client_build[0].get("type"):
        if use_spin_build:
            LOGGER.info("Skipping frosty build fetch for Spin build.")
        else:
            LOGGER.info("Fetching frosty build...")
            dest_root = local_paths.get_local_frosty_path()
            client_build_dest = os.path.join(dest_root, "Client")

            for cbi in client_build:
                _filer.fetch_frosty_build_by_source(
                    source=cbi.get("source"),
                    dest=os.path.join(client_build_dest, cbi.get("platform")),
                )

            if server_is_needed:
                server_build_dest = os.path.join(dest_root, "Server")
                _filer.fetch_frosty_build_by_source(
                    source=server_build["source"],
                    dest=server_build_dest,
                )
    elif not is_frosted:
        # Fetch pipeline binary
        LOGGER.info("Fetching code binaries...")
        if cook_type_enum not in (CookTypes.NO_COOK, CookTypes.AVALANCHE_STORAGE):
            _filer.fetch_code(
                code_branch,
                code_changelist,
                "pipeline",
                "release",
                custom_tag=pipeline_custom_tag,
            )

        if clean:
            client_builder.clean()

        if build_type == "dll":
            _filer.fetch_code(
                code_branch,
                code_changelist,
                "win64-dll",
                "release",
                mirror=False,
                fetch_tests=fetch_tests,
                custom_tag=custom_tag,
            )
        else:
            # Fetch game binaries.
            code_platform = platform
            if platform.lower() == "win64":
                code_platform = "win64game"
            _mirror = bool(config != "release")
            _filer.fetch_code(
                code_branch,
                code_changelist,
                code_platform,
                config,
                fetch_tests=fetch_tests,
                mirror=_mirror,
                custom_tag=custom_tag,
            )

        # Fetch game server binaries and cook if needed
        if server_is_needed:
            _filer.fetch_code(
                code_branch,
                code_changelist,
                server_platform,
                server_config,
                custom_tag=custom_tag,
            )

        # Import previous Avalanche state
        if import_avalanche_state:
            # We don't want to cook with the import args because its uses the same args for
            # both client and server builds
            LOGGER.info("Importing avalanche state for client...")
            client_import_args = import_avalanche_data_state(
                data_branch, code_branch, platform, _filer, data_changelist
            )

            if server_is_needed:
                LOGGER.info("Importing avalanche state for server...")
                server_extra_args += import_avalanche_data_state(
                    data_branch, code_branch, server_platform, _filer, data_changelist
                )

            if not server_is_needed or not icepick_cook:
                client_extra_args += client_import_args
        # Cook Steps
        if cook_type_enum == CookTypes.COOK:
            LOGGER.info("Normal client data cook...")
            client_builder.cook(
                pipeline_args=client_extra_args,
                collect_mdmps=True,
                clean_master_version_check=clean_master_version_check,
            )
            if server_is_needed and server_assets:
                LOGGER.info("Normal server data cook...")
                server_builder.cook(
                    pipeline_args=server_extra_args,
                    collect_mdmps=True,
                    clean_master_version_check=clean_master_version_check,
                )
    elif is_frosted:
        _filer = authenticate_filer(_filer, secret_context, target_build_share=target_build_share)
        frosted_platforms = ["pipeline", "frosted"]
        if build_type == "dll":
            frosted_platforms.append("win64-dll")
        if additional_tools_to_include:
            frosted_platforms.extend(additional_tools_to_include)
        for frosted_platform in frosted_platforms:
            _filer.fetch_code(
                code_branch,
                code_changelist,
                frosted_platform,
                "release",
                mirror=False,
                fetch_tests=False,
                custom_tag=custom_tag,
                target_build_share=target_build_share,
                use_bilbo=False,
            )
        client_extra_args.extend(["-forceDebugTarget", "-includeTestDataModules", "true"])
        client_builder.run_indexing(pipeline_args=client_extra_args)

    if cook_type_enum == CookTypes.ICEPICK_COOK:
        LOGGER.info("Icepick data cook...")
        client_icepick_cook_args = client_extra_args + [frostbite_core.get_emit_arg()]
        icepick_cook_args = list(client_icepick_cook_args)
        if clean_master_version_check:
            icepick_cook_args += data.DataUtils.get_clean_master_version_args()
        icepick.IcepickUtils.run_icepick_cook(
            platform,
            test_suite_names,
            config,
            icepick_cook_args,
            ignore_icepick_exit_code=False,
            extra_framework_args=extra_framework_args,
            settings_files_list=icepick_settings_files_list,
            enable_hailstorm=enable_hailstorm,
        )
    elif cook_type_enum == CookTypes.AVALANCHE_STORAGE:
        try:
            storage_server = SETTINGS.get("avalanche_state_host")[platform]
            db_name = avalanche.get_temp_db_name(
                platform, data_changelist, code_changelist, data_branch, db_name_prefix
            )
            icepick_run_args.extend(["--storage-server", storage_server, "--databaseId", db_name])
        except ConfigValueNotFoundException:
            LOGGER.error("Can not find avalanche host for {0} in setting.".format(platform))

    if cadet_activate_toolset and is_frosted:
        fbcli.run("install", ["houdini"])
        fbcli.run("install", ["cadet"])
        if fb_p4_user and fb_p4_password:
            LOGGER.info(
                "Cadet set credentials for user: {} and port: {}".format(fb_p4_user, fb_p4_port)
            )
            fbcli.run(
                "cadet",
                [
                    "set-credentials",
                    cadet_p4_port,
                    "--proxy-address",
                    fb_p4_port,
                    "--login",
                    fb_p4_user,
                    "--password",
                    fb_p4_password,
                ],
            )
        fbcli.run("cadet", ["activate-toolset"])

    test_results = ""
    test_suite_has_failed = False

    update_suite_name = True
    if core_licensee_id.lower() in ["frostbite"]:
        update_suite_name = False

    for _licensee in map(str.lower, frostbite_licensee):
        if _licensee in ["casablanca", "battlefieldgame"]:
            update_suite_name = False

    for test_suite in test_suites:
        decoded_test_suite = json.loads(test_suite)
        test_suite_name = decoded_test_suite["name"]
        LOGGER.info("Running test suite: {}".format(test_suite_name))

        if update_suite_name:
            test_suite_name = os.path.join(frostbite_core.get_game_data_dir(), test_suite_name)

        test_suite_run_args = icepick_run_args.copy()
        if "extra-args" in decoded_test_suite:
            test_suite_run_args.extend(decoded_test_suite["extra-args"])
        LOGGER.info("-- test_suite_run_args --")
        for arg in test_suite_run_args:
            LOGGER.info(arg)
        LOGGER.info("---!---")

        # common arguments for register_autotest_results
        autotest_results_args = {
            "should_register_in_bilbo": run_bilbo,
            "code_branch": code_branch,
            "code_changelist": code_changelist,
            "data_branch": data_branch,
            "data_changelist": data_changelist,
            "test_definition": test_definition,
            "test_suite": test_suite_name,
            "platform": platform,
        }

        try:
            LOGGER.info("Test suite: " + test_suite_name)
            autotest_utils.register_autotest_results(
                test_status="inprogress", **autotest_results_args
            )

            test_suite_data = (
                f"code_changelist:{code_changelist};" f"data_changelist:{data_changelist};"
            )
            if custom_test_suite_data:
                test_suite_data += f"{custom_test_suite_data};"

            LOGGER.info("Ignoring 'lease' value and passing 'None': {}".format(lease))

            # Get the current process
            current_process = psutil.Process()

            # Get the parent process
            parent_process = current_process.parent()

            if parent_process is not None:
                get_environment_variables("env_variables.log", parent_process)
            else:
                print("No parent process found.")

            get_environment_variables("fbenv_variables.log", current_process)

            LOGGER.info(
                "Running test suite {} {} times. All runs must be successful".format(
                    test_suite_name, num_test_runs
                )
            )
            for _ in range(int(num_test_runs)):
                current_run_args = test_suite_run_args.copy()
                LOGGER.info("Test run number: {}".format(_ + 1))
                icepicker.run_icepick(
                    platform=platform,
                    test_suite=test_suite_name,
                    test_group=test_group,
                    config=config,
                    settings_file_list=icepick_settings_files_list,
                    send_frosting_report=frosting_report,
                    lease=None,
                    build_type=build_type,
                    autobuild=autobuild,
                    run_args=current_run_args,
                    ignore_icepick_exit_code=False,
                    cook=False,
                    extra_framework_args=extra_framework_args,
                    custom_test_suite_data=test_suite_data,
                )

            test_results += "- {} successful\n".format(test_suite_name)
            autotest_utils.register_autotest_results(
                test_status="successful", **autotest_results_args
            )

        except Exception as exc:
            if fbenv_layer.is_api_function_failed_exception(exc):
                test_suite_has_failed = True
                test_results = autotest_utils.handle_failure(
                    test_results, test_suite_name, autotest_results_args
                )
                if exc.code < 0:
                    LOGGER.error(
                        "Something went wrong when running test suite {}. "
                        "Test results this far:\n{}".format(test_suite_name, test_results)
                    )
                    raise
                LOGGER.warning(exc)
            else:
                test_results = autotest_utils.handle_failure(
                    test_results, test_suite_name, autotest_results_args
                )
                LOGGER.exception(
                    "Unexpected error when running test suite {}, "
                    "Test results this far:\n{}".format(test_suite_name, test_results)
                )
                raise
        finally:
            save_icepick_logs()

    if not is_frosted:
        metadata_manager.dump_attributes(
            filer_paths.get_code_build_root_path(code_branch, code_changelist), None, None
        )

    LOGGER.info("Summary\n------\nRunning tests done. Results:\n{}\n---!---".format(test_results))
    if show_test_results and test_suite_has_failed:
        raise ELIPYException(
            "show_test_results_on_jenkins has been set to true. "
            "At least one test suite failed. Failing build."
        )


def get_build_from_bilbo(
    build_id: str,
    use_shift_build: Optional[bool] = False,
    use_spin_build: Optional[bool] = False,
    is_frosted: Optional[bool] = False,
) -> Dict:
    """
    Get the build entry from bilbo

    :param build_id: String matching bilbo ID - usually the build path
    :param use_shift_build: If True, use a build that was uploaded to Shift
    :param use_spin_build: If True, use a Spin build
    :param is_frosted: If True, use a Drone build uploaded to Azure
    """
    if build_id is None:
        return dict()

    metadata_manager = build_metadata_utils.setup_metadata_manager()
    builds = list(metadata_manager.get_build_by_id(build_id))

    if use_shift_build:
        LOGGER.info("Build exists and should have been uploaded to Shift.")
        return builds[0].source

    if use_spin_build:
        LOGGER.info("Build exists and should have been uploaded to Spin.")
        return builds[0].source

    if is_frosted:
        LOGGER.info("Build exists and should have been uploaded to Azure.")
        return builds[0].source

    builds = list(filter(lambda build: "deleted" not in build.source, builds))
    if not builds:
        raise ELIPYException("All builds were deleted.")

    LOGGER.info("Using the following build:\n%s", builds[0].source)
    return builds[0].source


def get_environment_variables(log_file, process):
    """
    Dump the environment variable from current process to log file

    :log_file: file name to write the environment variables
    :process: process object to get the environment variables
    """
    # Define keywords that might indicate a secret
    secret_keywords = ["SECRET", "PASSWORD", "PASSWD"]
    # Get the environment variables of the parent process
    _env = process.environ()

    # Print each environment variable
    with open(os.path.join(frostbite_core.get_tnt_root(), log_file), "w") as file:
        for key, value in _env.items():
            if not any(keyword in key.upper() for keyword in secret_keywords):
                file.write(f"{key}: {value}\n")
    LOGGER.info(
        "Environment variables from process {} have been written to {}.".format(
            process.pid, log_file
        )
    )
