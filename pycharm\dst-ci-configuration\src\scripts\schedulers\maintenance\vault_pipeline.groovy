package scripts.schedulers.maintenance

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * vault_pipeline.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Vaulting jobs') {
            steps {
                script {
                    def args = [
                        string(name: 'code_branch', value: params.code_branch),
                        string(name: 'data_branch', value: params.data_branch),
                        string(name: 'code_changelist', value: params.code_changelist),
                        string(name: 'data_changelist', value: params.data_changelist),
                        string(name: 'version', value: params.version),
                        string(name: 'build_location', value: params.build_location),
                        string(name: 'vault_verification_location', value: params.vault_verification_location),
                        string(name: 'additional_baseline_locations', value: params.additional_baseline_locations),
                    ]
                    def jobs = [:]

                    currentBuild.displayName = "${env.JOB_NAME}.${params.data_changelist}.${params.code_changelist}"
                    def final_result = Result.SUCCESS
                    def branchfile = GetBranchFile.get_branchfile(project.name, params.data_branch)
                    def expression_debug_data = branchfile.standard_jobs_settings.expression_debug_data ? ' --expression-debug-data' : ''
                    args += [string(name: 'expression_debug_data', value: expression_debug_data)]

                    def vault_win64_trial = params.vault_win64_trial ? ' --win64-trial' : ' --no-win64-trial'
                    args += [string(name: 'vault_win64_trial', value: vault_win64_trial)]

                    def verify_post_vault = params.verify_post_vault ? ' --verify-post-vault' : ''
                    args += [string(name: 'verify_post_vault', value: verify_post_vault)]

                    ['build'].each { job_type ->
                        LibCommonCps.VAULT_PLATFORMS.each { platform ->
                            if (params[platform]) {
                                jobs[platform + job_type] = {
                                    def downstream_job = build(
                                        job: "vault.${project.name}.${project.short_name}.${job_type}",
                                        parameters: args + [string(name: 'platform', value: platform)],
                                        propagate: false,
                                    )
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                    }

                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#cobra-outage-vault', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
