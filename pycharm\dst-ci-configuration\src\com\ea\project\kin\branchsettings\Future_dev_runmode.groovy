package com.ea.project.kin.branchsettings

class Future_dev_runmode {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                                            : 'RunmodeLevels',
        data_reference_job                               : 'kin-dev.data.start',
        enable_lkg_p4_counters                           : true,
        extra_datapreflight_args                         : ' --pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true ',
        import_avalanche_state                           : false,
        linux_docker_images                              : false,
        offsite_basic_drone_zip_builds                   : true,
        offsite_basic_drone_zip_builds_force_zip_override: true,
        poolbuild_data                                   : true,
        poolbuild_frosty                                 : true,
        remote_masters_to_receive_code                   : [[name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_to_receive_data                   : [[name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        server_asset                                     : 'RunmodeLevels',
        shift_branch                                     : true,
        shift_reference_job                              : 'future-dev-runmode.frosty.start',
        job_label_statebuild                             : 'statebuild',
        statebuild_data                                  : true,
        // autotest_remote_settings               : [
        //     p4_code_server            : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        //     p4_code_creds             : 'dice-p4buildedge03-fb',
        // ],
        slack_channel_code                               : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data                               : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchdata                          : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty                        : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_data                               : 6,
        upgrade_data_job                                 : true,
        use_deprecated_blox_packages                     : true,
        webexport_branch                                 : true,
        use_linuxclient                                  : true,
        skip_icepick_settings_file                       : true,
    ]
    static Map preflight_settings = [
        concurrent_data            : 4,
        pre_preflight              : true,
        statebuild_datapreflight   : false,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        datapreflight_reference_job: 'future-dev-runmode.data.lastknowngood',
        trigger_type               : 'none',
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = [
        [name: 'win64'],
        [name: 'xbsx'],
        [name: 'server'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = [
        [name: 'win64', platform: 'win64', assets: ['RunmodeLevels'], extra_label: ''],
        [name: 'xb1', platform: 'xb1', assets: ['RunmodeLevels'], extra_label: ''],
        [name: 'ps5', platform: 'ps5', assets: ['RunmodeLevels'], extra_label: ''],
        [name: 'server', platform: 'server', assets: ['RunmodeLevels'], extra_label: ''],
    ]
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
