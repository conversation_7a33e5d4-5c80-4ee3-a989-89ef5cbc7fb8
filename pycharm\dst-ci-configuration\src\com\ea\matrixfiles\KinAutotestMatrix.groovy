package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class KinAutotestMatrix extends AutotestMatrix {

    private static final String KIN_DEV = 'kin-dev'
    private static final String KIN_DEV_UNVERIFIED = 'kin-dev-unverified'
    private static final String KIN_STAGE = 'kin-stage'
    private static final String KIN_RELEASE = 'kin-release'
    private static final String FUTURE_DEV_CONTENT = 'future-dev-content'
    private static final String FUTURE_DEV_RUNMODE = 'future-dev-runmode'

    /*
    Some default values that can be used across lots of tests
     */
    private static final List EXTRA_ARGS_V1 = ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
    private static final List EXTRA_ARGS_V2 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
    private static final List EXTRA_ARGS_V3 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--max-parallel-tests', 1, '--override-server-build-configuration', 'final']
    private static final List EXTRA_ARGS_V4 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--max-parallel-tests', 3, '--override-server-build-configuration', 'final']
    private static final List EXTRA_ARGS_V5 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push']
    private static final List EXTRA_ARGS_V6 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--override-server-build-configuration', 'final']
    private static final List EXTRA_ARGS_V7 = ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-server-platform', 'linux']
    /*
    private static final List EXTRA_ARGS_V8 = ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360, '--max-parallel-tests', 2, '--default-server-platform', 'linux']
    */
    private static final List EXTRA_ARGS_V9 = ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--max-parallel-tests', 2]

    private static final List DEFAULT_PLATFORMS = [
        new Platform(name: Name.WIN64),
        new Platform(name: Name.XB1),
        new Platform(name: Name.PS4),
        new Platform(name: Name.XBSX),
        new Platform(name: Name.PS5),
    ]

    private static final List DEFAULT_PLATFORMS_EU = [
        new Platform(name: Name.WIN64, region: Region.WW),
        new Platform(name: Name.XBSX, region: Region.WW),
        new Platform(name: Name.XB1, region: Region.WW),
        new Platform(name: Name.PS4, region: Region.EU),
        new Platform(name: Name.PS5, region: Region.EU),
    ]

    private static final List DEFAULT_PLATFORMS_DEV = [
        new Platform(name: Name.WIN64, region: Region.WW),
        new Platform(name: Name.XBSX, region: Region.WW),
        new Platform(name: Name.XB1, region: Region.WW),
        new Platform(name: Name.PS4, region: Region.EU),
        new Platform(name: Name.PS5, region: Region.DEV),
    ]

    private static final List DEFAULT_PLATFORMS_EXCEPT_WIN64 = [
        new Platform(name: Name.XB1),
        new Platform(name: Name.PS4),
        new Platform(name: Name.XBSX),
        new Platform(name: Name.PS5),
    ]

    private static final List GEN5_PLATFORMS = [
        new Platform(name: Name.WIN64),
        new Platform(name: Name.XBSX),
        new Platform(name: Name.PS5),
    ]

    private final Map DEFAULT_SLACK_SETTINGS = [channels: [], skip_for_multiple_failures: true]

    private final Map KIN_SLACK_SETTINGS = [
        channels                  : ['#clockwork-notify'],
        skip_for_multiple_failures: true,
    ]

    /*
    Define sets of tests to run for different types of autotests.
    Add more setups here if needed.
    */

    // Setup for tasks streams

    private final TestInfo tooltests = new TestInfo(
        platforms: [new Platform(name: Name.WIN64)],
        testGroup: 'tooltests',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        jobLabel: 'tooltests',
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'KingstonUnitTests', platforms: [new Platform(name: Name.WIN64)], poolType: ''),
            new TestSuite(name: 'KingstonIntegrationTests', platforms: [new Platform(name: Name.WIN64)], poolType: '', extraArgs: ['--attach-atftargets-log-to-test-suite', 'true', '--fetch-target-tty', 'true', '--add-target-tty-to-atf-targets-log', 'true']),
        ]
    )

    private final TestInfo dynamotests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'dynamotests',
        timeoutHours: 20,
        tests: [
            new TestSuite(name: 'A2B_Vehicles_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_CallIns_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_CharacterTrait_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_GameMode_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_GameOpening_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_InteractiveObject_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_OpenGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_Throwable_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_Weapon_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_WeaponProficiency_Tests', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo boottests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'boottests',
        timeoutHours: 12,
        tests: [
            new TestSuite(name: 'BootFlowStartupLevel', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'OnlineBootAndDeploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndReloadLevelTest', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndReload_AutoPlaytest', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployDiscarded', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployIrreversible', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployLonghaul', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployTheWall', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployKaleidoscope', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployTestRanges', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployHourglass', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployFrost', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployLightHouse', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployOasis', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployPort', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployRural', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployRidge', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployDrained', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployLightsOut', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployBoulder', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'BootAndDeployScarred', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
        ]
    )

    private final TestInfo boottests_stage = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'boottests',
        timeoutHours: 12,
        tests: [
            new TestSuite(name: 'BootAndDeploySalem', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_windows_stage = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 27,
        tests: [
            new TestSuite(name: 'OnlineJoinFlowTests', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'OnlineEndOfRound', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'CrashReporting', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'AudioTests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FT_BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_InteractiveObject_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameOpening_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_FrontEnd_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCallIns', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeapons', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaGadgets', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaVehicles', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCharacterKits', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_BasicMicaSoldierFunctionality', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_SoldierUI', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests'),
            new TestSuite(name: 'A2B_OpenGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WorldKeyEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'AutoPlayTestSoakSmoke', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_MeleeWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Storms', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ScoringEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ClassGadget_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_StationaryWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_SquadManagement', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponProficiency_Tests', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_windows_release = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 27,
        tests: [
            new TestSuite(name: 'OnlineJoinFlowTests', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'OnlineEndOfRound', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'CrashReporting', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'AudioTests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FT_BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_InteractiveObject_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameOpening_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_FrontEnd_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCallIns', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeapons', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaGadgets', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaVehicles', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCharacterKits', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_BasicMicaSoldierFunctionality', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_SoldierUI', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests'),
            new TestSuite(name: 'A2B_OpenGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WorldKeyEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'AutoPlayTestSoakSmoke', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_MeleeWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Storms', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ScoringEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ClassGadget_Tests', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_windows_seasonal_stage = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 30,
        tests: [
            new TestSuite(name: 'A2B_Throwable_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_windows_seasonal_stage_set_two = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 27,
        tests: [
            new TestSuite(name: 'A2B_GameMode_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_set_one_stage = new TestInfo(
        platforms: DEFAULT_PLATFORMS_EXCEPT_WIN64,
        parallelLimit: 4,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 27,
        tests: [
            new TestSuite(name: 'AudioTests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaGadgets', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCallIns', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaCharacterKits', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_BasicMicaSoldierFunctionality', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeapons', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaVehicles', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_SoldierUI', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests'),
            new TestSuite(name: 'A2B_OpenGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WorldKeyEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'AutoPlayTestSoakSmoke', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Vehicles_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterGadgets_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Storms', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ScoringEvents_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_ClassGadget_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_SquadManagement', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponProficiency_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_set_two_stage = new TestInfo(
        platforms: DEFAULT_PLATFORMS_EXCEPT_WIN64,
        parallelLimit: 4,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 27,
        tests: [
            new TestSuite(name: 'OnlineJoinFlowTests', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'A2B_CallIns_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameOpening_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InteractiveObject_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_FrontEnd_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'CrashReporting', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'OnlineEndOfRound', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_Soldier_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_MeleeWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_StationaryWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season7', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo featuretests_set_two_release = new TestInfo(
        platforms: DEFAULT_PLATFORMS_EXCEPT_WIN64,
        parallelLimit: 4,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 22,
        tests: [
            new TestSuite(name: 'OnlineJoinFlowTests', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'A2B_CallIns_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameOpening_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InteractiveObject_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_FrontEnd_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'CrashReporting', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'FT_CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'OnlineEndOfRound', extraArgs: EXTRA_ARGS_V2, needGameServer: true),
            new TestSuite(name: 'A2B_DICEAI_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_Soldier_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'FeatureTest_MicaWeaponAttachments', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_MeleeWeapon_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Throwable_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CharacterTrait_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_GameMode_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Soldier_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season4', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_Weapons_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_CallIns_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_DICEAI_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_OpenGadgets_Tests_Season5', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo gamemodetests_release = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'gamemodetests',
        timeoutHours: 18,
        tests: [
            new TestSuite(name: 'PreRoundTests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'KinModeHopper', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'ZoneStreamingCapture', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: [
                    new Platform(name: Name.XB1),
                    new Platform(name: Name.PS4),
                    new Platform(name: Name.WIN64),
                ]
            ),
            new TestSuite(name: 'GameModeTestFrost', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'GameModeTestHarbor', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'GameModeTestLightHouse', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'GameModeTestOasis', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'GameModeTestPort', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'GameModeTestRural', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_InsertionFlow_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_GameMode_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_GameMode_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
        ]
    )

    private final TestInfo gamemodetests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'gamemodetests',
        timeoutHours: 18,
        tests: [
            new TestSuite(name: 'PreRoundTests', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'KinModeHopper', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'ZoneStreamingCapture', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: [
                    new Platform(name: Name.XB1),
                    new Platform(name: Name.PS4),
                    new Platform(name: Name.WIN64),
                ]
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: true,
                platforms: GEN5_PLATFORMS
            ),
            new TestSuite(name: 'A2B_GameMode_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_GameMode_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_GameMode_Tests_Mica', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'A2B_GameMode_Tests_Season6', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_InsertionFlow_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
        ]
    )

    private final TestInfo autoplaytesttests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 10,
        runLevelsInParallel: true,
        serverPlatform: 'linuxserver',
        testGroup: 'autoplaytesttests',
        timeoutHours: 16,
        tests: [
            new TestSuite(name: 'AutoPlayTestOrbital', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestLongHaul', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestHourglass', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestIrreversible', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestKaleidoscope', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestTheWall', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestDiscarded', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestRidge', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestLightsOut', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestMicaMutators', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestModBuilder', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'DiceAIAutoPlayTest', extraArgs: EXTRA_ARGS_V7, needGameServer: true,
                platforms: DEFAULT_PLATFORMS_EXCEPT_WIN64
            ),
            new TestSuite(name: 'AutoPlayTestDrained', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestBoulder', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
            new TestSuite(name: 'AutoPlayTestScarred', extraArgs: EXTRA_ARGS_V7, needGameServer: true),
        ]
    )

    private final TestInfo attachmenttests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 25,
        tests: [
            new TestSuite(name: 'A2B_WeaponAttachment_Tests', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season1', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season2', extraArgs: EXTRA_ARGS_V1),
            new TestSuite(name: 'A2B_WeaponAttachment_Tests_Season3', extraArgs: EXTRA_ARGS_V1),
        ]
    )

    private final TestInfo weaponskincustomizationtests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 48,
        tests: [
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season4', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season6', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponSkinCustomization_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo weaponcharmcustomizationtests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 48,
        tests: [
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season4', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season6', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_WeaponCharmCustomization_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo vehicleskincustomizationtests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 48,
        tests: [
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season4', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season6', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleSkinCustomization_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo vehicledecalcustomizationtests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 48,
        tests: [
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season4', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season6', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_VehicleDecalCustomization_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo specialistskincustomizationtests = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 48,
        tests: [
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season1', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season2', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season3', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season4', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season5', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season6', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
            new TestSuite(name: 'A2B_SpecialistSkinCustomization_Tests_Season7', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo weapondamagetests = new TestInfo(
        platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'A2B_WeaponDamageValueTests', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo performance_minspec_tests = new TestInfo(
        testGroup: 'performancetests',
        platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'PerformanceFlythrough_MinSpec', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--use-remote-file-share-handling', 'true', '--max-parallel-tests', 1, '--override-server-build-configuration', 'final']),
            new TestSuite(name: 'PerformanceAutoPlayTests_MinSpec', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--use-remote-file-share-handling', 'true', '--max-parallel-tests', 1, '--override-server-build-configuration', 'final']),
        ]
    )

    private final TestInfo performance_recspec_tests = new TestInfo(
        testGroup: 'performancetests',
        platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests_RecSpec', timeoutHours: 3, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--max-parallel-tests', 1]),
            new TestSuite(name: 'PerformanceBenchmark', timeoutHours: 3, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'recommended_spec_pool', '--max-parallel-tests', 1]),
        ]
    )

    private final TestInfo performance_recspec_tests_unverified = new TestInfo(
        testGroup: 'performancetests',
        platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests_RecSpec', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3),
            new TestSuite(name: 'PerformanceFlythrough_RecSpec', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3),
            new TestSuite(name: 'PerformanceBenchmark', timeoutHours: 3, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'recommended_spec_pool', '--max-parallel-tests', 1, '--override-server-build-configuration', 'final']),
        ]
    )

    private final TestInfo performance_high_priority_kin_release = new TestInfo(
        timeoutHours: 2,
        platforms: DEFAULT_PLATFORMS_EU,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 3, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_high_priority_kin_stage = new TestInfo(
        timeoutHours: 8,
        platforms: DEFAULT_PLATFORMS_EU,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Extended_Stage', timeoutHours: 2, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season05', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
        ]
    )

    private final TestInfo performance_high_priority_unverified = new TestInfo(
        runLevelsInParallel: true,
        parallelLimit: 5,
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.XB1, region: Region.WW),
            new Platform(name: Name.PS4, region: Region.EU),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season04', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season05', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season06', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season07', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
        ]
    )

    private final TestInfo performance_low_priority_future_dev_content = new TestInfo(
        runLevelsInParallel: true,
        parallelLimit: 5,
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.XB1, region: Region.WW),
            new Platform(name: Name.PS4, region: Region.EU),
            new Platform(name: Name.PS5, region: Region.EU),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythroughIwoJima', timeoutHours: 3, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_medium_priority_future_dev_content = new TestInfo(
        runLevelsInParallel: true,
        parallelLimit: 5,
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.XB1, region: Region.WW),
            new Platform(name: Name.PS4, region: Region.EU),
            new Platform(name: Name.PS5, region: Region.EU),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests_Season08', timeoutHours: 3, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
        ]
    )

    private final TestInfo performance_medium_priority = new TestInfo(
        timeoutHours: 14,
        runLevelsInParallel: true,
        parallelLimit: 5,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.XB1, region: Region.WW),
            new Platform(name: Name.PS4, region: Region.EU),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythroughOrbital', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughLonghaul', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughIrreversible', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughHourglass', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughTheWall', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughRidge', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughKaleidoscope', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughDiscarded', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughLightsOut', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughDrained', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughBoulder', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughScarred', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughSalem', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughCassava', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughStadium', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_medium_priority_release = new TestInfo(
        timeoutHours: 4,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythrough_Season04Detailed', timeoutHours: 2, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythrough_Season05Detailed', timeoutHours: 2, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_medium_priority_release_future = new TestInfo(
        timeoutHours: 2,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythrough_Season08Detailed', timeoutHours: 2, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_medium_priority_rtao = new TestInfo(
        timeoutHours: 4,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythrough_RTAO', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_rtx_30_series_pool', '--override-server-build-configuration', 'final'],
                platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
            ),
            new TestSuite(name: 'PerformanceAutoPlayTests_RTAO', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_rtx_30_series_pool', '--override-server-build-configuration', 'final'],
                platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
            ),
        ]
    )

    private final TestInfo performance_low_priority_kin_stage = new TestInfo(
        timeoutHours: 4,
        platforms: DEFAULT_PLATFORMS_EU,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceCongoTests', timeoutHours: 3, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceControllablesExplosion', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughSalem', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughCassava', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_low_priority_set_one_unverified = new TestInfo(
        timeoutHours: 10,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests_Extended', timeoutHours: 2, extraArgs: EXTRA_ARGS_V4, needGameServer: true),
            new TestSuite(name: 'PerformanceBasicLoadTimeTests', timeoutHours: 1, extraArgs: EXTRA_ARGS_V4, needGameServer: true),
            new TestSuite(name: 'PerformanceBenchmark', timeoutHours: 2, extraArgs: EXTRA_ARGS_V4, needGameServer: true,
                platforms: [
                    new Platform(name: Name.XBSX, region: Region.WW),
                    new Platform(name: Name.XB1, region: Region.WW),
                    new Platform(name: Name.PS4, region: Region.EU),
                    new Platform(name: Name.PS5, region: Region.DEV),
                ],
            ),
            new TestSuite(name: 'PerformanceLoadTimeTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V4, needGameServer: true),
            new TestSuite(name: 'PerformanceUITests', timeoutHours: 1, extraArgs: EXTRA_ARGS_V4, needGameServer: true,
                platforms: [
                    new Platform(name: Name.WIN64, region: Region.WW),
                    new Platform(name: Name.XBSX, region: Region.WW),
                    new Platform(name: Name.XB1, region: Region.WW),
                    new Platform(name: Name.PS5, region: Region.DEV),
                ]
            ),
        ]
    )

    private final TestInfo performance_low_priority_set_two_unverified = new TestInfo(
        timeoutHours: 10,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceCongoTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceMicaAutoPlayTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceMicaCongoTests', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'PerformanceControllablesExplosion', timeoutHours: 1, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'MemoryTrack_Playtest', timeoutHours: 2, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
        ]
    )

    private final TestInfo performance_low_priority_release = new TestInfo(
        timeoutHours: 6,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'MemoryTrackFlythrough_Short', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythrough_MicaDetailed', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_low_priority_release_night = new TestInfo(
        timeoutHours: 6,
        platforms: DEFAULT_PLATFORMS_DEV,
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceFlythroughOasis_Night', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceFlythroughFrost_Night', timeoutHours: 1, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    private final TestInfo performance_neo = new TestInfo(
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.PS4, region: Region.EU),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_neo_testing_pool', '--override-server-build-configuration', 'final']),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season01', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_neo_testing_pool', '--override-server-build-configuration', 'final']),
        ]
    )

    private final TestInfo performance_scorpio = new TestInfo(
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.XB1, region: Region.WW),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_xb1x_testing_pool', '--override-server-build-configuration', 'final']),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season01', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_xb1x_testing_pool', '--override-server-build-configuration', 'final']),
            new TestSuite(name: 'PerformanceFlythrough', timeoutHours: 1, extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-client-target-group-overrides', 'shared_xb1x_testing_pool', '--override-server-build-configuration', 'final']),
        ]
    )

    private final TestInfo checkmatetests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'Checkmate_BootAndDeploy_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Ridge', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Ridge', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_DeployAndPauseMenu', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_FrontEnd', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Drained', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Drained', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_LightsOut', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_LightsOut', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_CoreDesign', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Scarred', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Scarred', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Salem', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Salem', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo checkmatetests_stage = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'Checkmate_BootAndDeploy_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_DeployAndPauseMenu', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_FrontEnd', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Scarred', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Scarred', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Salem', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Salem', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo checkmatetests_release = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'Checkmate_BootAndDeploy_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Discarded', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Hourglass', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_DeployAndPauseMenu', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_UIScreen_FrontEnd', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Boulder', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo lkgautoplaytests = new TestInfo(
        platforms: GEN5_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkgautoplaytests',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'LKG_AutoplayTest_Discarded', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Hourglass', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Irreversible', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Kaleidoscope', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Longhaul', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Orbital', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Ridge', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_TheWall', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Drained', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_LightsOut', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Boulder', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Scarred', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo lkgautoplaytests_unverified = new TestInfo(
        platforms: GEN5_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkgautoplaytests',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'LKG_AutoplayTest_Discarded', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Hourglass', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Irreversible', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Kaleidoscope', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Longhaul', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Orbital', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Ridge', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_TheWall', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Drained', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_LightsOut', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Boulder', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
            new TestSuite(name: 'LKG_AutoPlayTest_Scarred', extraArgs: ['--rest-port', '5143', '--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360]
            ),
        ]
    )

    private final TestInfo lkgautoplaytests_stage = new TestInfo(
        platforms: GEN5_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkgautoplaytests',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'LKG_AutoplayTest_Discarded', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Hourglass', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Irreversible', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Kaleidoscope', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Longhaul', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Orbital', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Ridge', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_TheWall', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Drained', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_LightsOut', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Scarred', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'LKG_AutoPlayTest_Salem', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo lkgtests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
            new Platform(name: Name.XB1),
            new Platform(name: Name.PS4),
        ],
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'lkgtests',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'CriticalPathTest_Deploy_LinuxServer', extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-server-platform', 'linux'],
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)],
            ),
            new TestSuite(name: 'LevelDestruction', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Critical_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.WIN64), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)],
            ),
            new TestSuite(name: 'LKG_Autoplayer_Tests', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo lkgtestskindevunverified = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
            new Platform(name: Name.XB1),
            new Platform(name: Name.PS4),
        ],
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'lkgtests',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'LevelDestruction', extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Critical_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.WIN64), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)],
            ),
            new TestSuite(name: 'LKG_Autoplayer_Tests', extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo lkgtestsfuture = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'Checkmate_BootAndDeploy_Drained', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_IwoJima', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Drained', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_LightsOut', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Longhaul', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Orbital', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Kaleidoscope', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_LightsOut', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_BootAndDeploy_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Checkmate_CriticalPath_Irreversible', extraArgs: EXTRA_ARGS_V2, needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo lkgtests_stage = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
            new Platform(name: Name.XB1),
            new Platform(name: Name.PS4),
        ],
        parallelLimit: 5,
        runLevelsInParallel: true,
        testGroup: 'lkgtests',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'CriticalPathTest_Deploy', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'LevelDestruction', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'Critical_BootAndDeploy_TheWall', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.WIN64), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)],
            ),
            new TestSuite(name: 'BootAndDeployHarbor', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'LKG_Autoplayer_Tests', extraArgs: EXTRA_ARGS_V2,
                platforms: [new Platform(name: Name.WIN64)],
            ),
        ]
    )

    private final TestInfo buildertests_set_one_stage = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        runLevelsInParallel: true,
        parallelLimit: 5,
        testGroup: 'buildertests',
        timeoutHours: 18,
        tests: [
            new TestSuite(name: 'AutoPlayTestMicaMutators', extraArgs: EXTRA_ARGS_V9),
            new TestSuite(name: 'AutoPlayTestModBuilder', extraArgs: EXTRA_ARGS_V9),
            //new TestSuite(name: 'BootAndDeployModBuilder', extraArgs: EXTRA_ARGS_V9),
            new TestSuite(name: 'FeatureTest_MicaVehicleLimits', extraArgs: EXTRA_ARGS_V9),
            new TestSuite(name: 'FeatureTest_MicaVehicleLimits2', extraArgs: EXTRA_ARGS_V9),
            new TestSuite(name: 'FeatureTest_MicaVehicleLimits3', extraArgs: EXTRA_ARGS_V9),
            new TestSuite(name: 'FeatureTest_MicaVehicleLimits4', extraArgs: EXTRA_ARGS_V9),
        ]
    )

    private final TestInfo buildertests_set_two_stage = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        testGroup: 'buildertests',
        runLevelsInParallel: true,
        parallelLimit: 5,
        timeoutHours: 18,
        tests: [
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary2', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary3', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary4', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators2', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators3', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators4', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
        ]
    )

    private final TestInfo buildertests_release = new TestInfo(
        platforms: DEFAULT_PLATFORMS,
        testGroup: 'buildertests',
        runLevelsInParallel: true,
        parallelLimit: 5,
        timeoutHours: 18,
        tests: [
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary2', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary3', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary4', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators2', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators3', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
            new TestSuite(name: 'FeatureTest_MicaMutators4', extraArgs: EXTRA_ARGS_V9, needGameServer: true),
        ]
    )

    private final TestInfo releasetests = new TestInfo(
        config: 'release',
        parallelLimit: 5,
        runLevelsInParallel: true,
        platforms: DEFAULT_PLATFORMS,
        testGroup: 'releasetests',
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'BootGameViewTests', platforms: [new Platform(name: Name.WIN64)], extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo linux_server_tests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        serverPlatform: 'linuxserver',
        testGroup: 'linux_server_tests',
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'CriticalPathTest_Deploy_LinuxServer', extraArgs: ['--timeout-client-level-load', '00:08:00', '--runtime-connect-timeout', 360, '--default-server-platform', 'linux'],
                platforms: [new Platform(name: Name.XB1), new Platform(name: Name.PS4), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)],
            )
        ]
    )

    private final TestInfo smoketests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'boottests',
        timeoutHours: 1,
        tests: [
            new TestSuite(name: 'OnlineBootAndDeploy', extraArgs: EXTRA_ARGS_V1, needGameServer: true),
            new TestSuite(name: 'OnlineBootAndDeployDrained', extraArgs: EXTRA_ARGS_V1, needGameServer: true)
        ]
    )

    private final TestInfo smoketests_performance = new TestInfo(
        timeoutHours: 5,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'PerformanceAutoPlayTests', timeoutHours: 3, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
            new TestSuite(name: 'PerformanceAutoPlayTests_Season02', timeoutHours: 3, extraArgs: EXTRA_ARGS_V5, needGameServer: true),
        ]
    )

    /*
    Collect the available setups for the different types of tests, and connect them to the branches that will use these.
    */
    private final Map collectingSlackSettings = [
        (DEFAULT)           : DEFAULT_SLACK_SETTINGS,
        (KIN_DEV)           : KIN_SLACK_SETTINGS,
        (KIN_DEV_UNVERIFIED): KIN_SLACK_SETTINGS,
        (KIN_RELEASE)       : KIN_SLACK_SETTINGS,
        (FUTURE_DEV_CONTENT): KIN_SLACK_SETTINGS,
        (FUTURE_DEV_RUNMODE): KIN_SLACK_SETTINGS,
    ]

    /* ********** ********** ********** ********** **********
    Define if the levels should be tested in parallel or not.
    */
    private final Map runLevelsInParallel = [
        (DEFAULT)           : true,
        (KIN_DEV)           : false,
        (KIN_DEV_UNVERIFIED): false,
        (KIN_STAGE)         : false,
        (KIN_RELEASE)       : false,
        (FUTURE_DEV_CONTENT): false,
        (FUTURE_DEV_RUNMODE): false,
    ]

    @Override
    List<String> getBranches() {
        return [KIN_DEV, KIN_DEV_UNVERIFIED, KIN_STAGE, KIN_RELEASE, FUTURE_DEV_CONTENT, FUTURE_DEV_RUNMODE]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return [
            new AutotestCategory(
                name: 'weapondamagetests',
                testDefinition: 'weapondamagetests',
                runBilbo: false,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: false,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, weapondamagetests, 'H 20 * * 4'),
                ]),
            new AutotestCategory(
                name: 'dynamotests',
                testDefinition: 'dynamotests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_RELEASE, dynamotests, ''),
                ]),
            new AutotestCategory(
                name: 'boottests',
                testDefinition: 'boottests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, boottests, 'H 5 * * 1-7'),
                    branchConfiguration(KIN_STAGE, boottests, 'H 2 * * 1-6'),
                    branchConfiguration(KIN_RELEASE, boottests, 'H 9 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'boottests_stage',
                testDefinition: 'boottests_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [

                    branchConfiguration(KIN_STAGE, boottests_stage, 'H 6 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'featuretests_windows_stage',
                testDefinition: 'featuretests_windows_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, featuretests_windows_stage, 'H 14 * * 4'),
                    branchConfiguration(KIN_RELEASE, featuretests_windows_release, ''),
                ]),
            new AutotestCategory(
                name: 'featuretests_windows_seasonal_stage',
                testDefinition: 'featuretests_windows_seasonal_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, featuretests_windows_seasonal_stage, 'H 21 * * 4'),
                    branchConfiguration(KIN_RELEASE, featuretests_windows_seasonal_stage, ''),
                ]),
            new AutotestCategory(
                name: 'featuretests_windows_seasonal_stage_set_two',
                testDefinition: 'featuretests_windows_seasonal_stage_set_two',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, featuretests_windows_seasonal_stage_set_two, 'H 2 * * 3'),
                    branchConfiguration(KIN_RELEASE, featuretests_windows_seasonal_stage_set_two, ''),
                ]),
            new AutotestCategory(
                name: 'featuretests_set_one_stage',
                testDefinition: 'featuretests_set_one_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, featuretests_set_one_stage, 'H 20 * * 1'),
                    branchConfiguration(KIN_RELEASE, featuretests_set_one_stage, ''),
                ]),
            new AutotestCategory(
                name: 'featuretests_set_two_stage',
                testDefinition: 'featuretests_set_two_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, featuretests_set_two_stage, 'H 15 * * 2'),
                    branchConfiguration(KIN_RELEASE, featuretests_set_two_release, ''),
                ]),
            new AutotestCategory(
                name: 'gamemodetests',
                testDefinition: 'gamemodetests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, gamemodetests, 'H 20 * * 1-6'),
                    branchConfiguration(KIN_STAGE, gamemodetests, 'H 10 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'gamemodetests_release',
                testDefinition: 'gamemodetests_release',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_RELEASE, gamemodetests_release, 'H 12 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'autoplaytesttests',
                testDefinition: 'autoplaytesttests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, autoplaytesttests, 'H 18 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'attachmenttests',
                testDefinition: 'attachmenttests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: false,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, attachmenttests, 'H 23 * * 4'),
                    branchConfiguration(KIN_RELEASE, attachmenttests, ''),
                ]),
            new AutotestCategory(
                name: 'weaponskincustomizationtests',
                testDefinition: 'weaponskincustomizationtests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, weaponskincustomizationtests, 'H 6 * * 1'),
                ]),
            new AutotestCategory(
                name: 'weaponcharmcustomizationtests',
                testDefinition: 'weaponcharmcustomizationtests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, weaponcharmcustomizationtests, 'H 6 * * 2'),
                ]),
            new AutotestCategory(
                name: 'vehicleskincustomizationtests',
                testDefinition: 'vehicleskincustomizationtests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, vehicleskincustomizationtests, 'H 0 * * 3'),
                ]),
            new AutotestCategory(
                name: 'vehicledecalcustomizationtests',
                testDefinition: 'vehicledecalcustomizationtests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, vehicledecalcustomizationtests, 'H 19 * * 4'),
                ]),
            new AutotestCategory(
                name: 'specialistskincustomizationtests',
                testDefinition: 'specialistskincustomizationtests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, specialistskincustomizationtests, 'H 6 * * 5'),
                ]),
            new AutotestCategory(
                name: 'checkmatetests',
                testDefinition: 'checkmatetests',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV, checkmatetests, 'H H/5 * * 1-6'),
                    branchConfiguration(KIN_DEV_UNVERIFIED, checkmatetests, 'H 1-23/5 * * 1-6'),
                    branchConfiguration(KIN_STAGE, checkmatetests_stage, 'H 0,8,14,20 * * 1-6'),
                    branchConfiguration(KIN_RELEASE, checkmatetests_release, 'H 1,4,10,16,20 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'lkgautoplaytests',
                testDefinition: 'lkgautoplaytests',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV, lkgautoplaytests, 'H 12 * * 1-6'),
                    branchConfiguration(KIN_DEV_UNVERIFIED, lkgautoplaytests_unverified, 'H 2 * * 1-6\nH * * * 7'),
                    branchConfiguration(KIN_STAGE, lkgautoplaytests_stage, 'H 20 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'lkgtests',
                testDefinition: 'lkgtests',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV, lkgtests, 'H H/5 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'lkgtests_stage',
                testDefinition: 'lkgtests_stage',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, lkgtests_stage, 'H 3,14,18,21 * * 1-6'),
                    branchConfiguration(KIN_RELEASE, lkgtests_stage, 'H 4,10,16,20 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'lkgtestskindevunverified',
                testDefinition: 'lkgtestskindevunverified',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, lkgtestskindevunverified, 'H * * * 1-6\nH H/6 * * 7'),
                ]),
            new AutotestCategory(
                name: 'lkgtestsfuture',
                testDefinition: 'lkgtestsfuture',
                enableP4Counters: true,
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(FUTURE_DEV_CONTENT, lkgtestsfuture, 'H 0 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'tooltests',
                testDefinition: 'tooltests',
                runBilbo: false,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, tooltests, 'H */2 * * 1-6\nH 6-22/2 * * 7'),
                ]),
            new AutotestCategory(
                name: 'buildertests_set_one_stage',
                testDefinition: 'buildertests_set_one_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, buildertests_set_one_stage, 'H 21 * * 1'),
                ]),
            new AutotestCategory(
                name: 'buildertests_set_two_stage',
                testDefinition: 'buildertests_set_two_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_STAGE, buildertests_set_two_stage, 'H 7 * * 3'),
                ]),
            new AutotestCategory(
                name: 'buildertests_release',
                testDefinition: 'buildertests_release',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_RELEASE, buildertests_release, ''),
                ]),
            new AutotestCategory(
                name: 'performancetests_minspec',
                testDefinition: 'performancetests_minspec',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_minspec_tests, 'H 0,4,16,20 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performancetests_recspec',
                testDefinition: 'performancetests_recspec',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_STAGE, performance_recspec_tests, 'H 0 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'performancetests_recspec_unverified',
                testDefinition: 'performancetests_recspec_unverified',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_recspec_tests_unverified, 'H 10 * * 1-6\nH 15 * * 7'),
                ]),
            new AutotestCategory(
                name: 'releasetests',
                testDefinition: 'releasetests',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, releasetests, 'H 9 * * 1-6'),
                    branchConfiguration(KIN_STAGE, releasetests, 'H 11 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'linux_server_tests',
                testDefinition: 'linux_server_tests',
                runBilbo: false,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, linux_server_tests, 'H 3 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_high_priority',
                testDefinition: 'performance_high_priority',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_RELEASE, performance_high_priority_kin_release, 'H 16 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_high_priority_unverified',
                testDefinition: 'performance_high_priority_unverified',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_high_priority_unverified, 'H 12 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_future_dev_content',
                testDefinition: 'performance_low_priority_future_dev_content',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(FUTURE_DEV_CONTENT, performance_low_priority_future_dev_content, 'H 20 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_high_priority_kin_stage',
                testDefinition: 'performance_high_priority_kin_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_STAGE, performance_high_priority_kin_stage, 'H 10 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_medium_priority',
                testDefinition: 'performance_medium_priority',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_medium_priority, 'H 4 * * 1-6'),
                    branchConfiguration(FUTURE_DEV_CONTENT, performance_medium_priority_future_dev_content, 'H 13 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'performance_medium_priority_release',
                testDefinition: 'performance_medium_priority_release',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'release',
                serverConfig: 'release',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_medium_priority_release, 'H 10 * * 1-6'),
                    branchConfiguration(FUTURE_DEV_CONTENT, performance_medium_priority_release_future, 'H 16 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'performance_medium_priority_rtao',
                testDefinition: 'performance_medium_priority_rtao',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_medium_priority_rtao, 'H 4 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_kin_stage',
                testDefinition: 'performance_low_priority_kin_stage',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_STAGE, performance_low_priority_kin_stage, 'H 13 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_set_one_unverified',
                testDefinition: 'performance_low_priority_set_one_unverified',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_low_priority_set_one_unverified, 'H 22 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_set_two_unverified',
                testDefinition: 'performance_low_priority_set_two_unverified',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_low_priority_set_two_unverified, 'H 4 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_neo',
                testDefinition: 'performance_neo',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_neo, 'H 22 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_scorpio',
                testDefinition: 'performance_scorpio',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_scorpio, 'H 22 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_release',
                testDefinition: 'performance_low_priority_release',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'release',
                serverConfig: 'release',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_low_priority_release, 'H 18 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_low_priority_release_night',
                testDefinition: 'performance_low_priority_release_night',
                runBilbo: true,
                // extraP4Server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
                needGameServer: true,
                config: 'release',
                serverConfig: 'release',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, performance_low_priority_release_night, 'H 18 * * 1-7'),
                ]),
        ]
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return [
            new AutotestCategory(
                name: 'weapondamagetests',
                testDefinition: 'weapondamagetests',
                runBilbo: false,
                needGameServer: false,
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV, weapondamagetests),
                    branchConfiguration(KIN_STAGE, weapondamagetests),
                    branchConfiguration(KIN_RELEASE, weapondamagetests),
                    branchConfiguration(FUTURE_DEV_CONTENT, weapondamagetests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'attachmenttests',
                testDefinition: 'attachmenttests',
                runBilbo: false,
                needGameServer: false,
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, attachmenttests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'weaponskincustomizationtests',
                testDefinition: 'weaponskincustomizationtests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, weaponskincustomizationtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'weaponcharmcustomizationtests',
                testDefinition: 'weaponcharmcustomizationtests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, weaponcharmcustomizationtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'vehicleskincustomizationtests',
                testDefinition: 'vehicleskincustomizationtests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, vehicleskincustomizationtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'vehicledecalcustomizationtests',
                testDefinition: 'vehicledecalcustomizationtests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, vehicledecalcustomizationtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'specialistskincustomizationtests',
                testDefinition: 'specialistskincustomizationtests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                config: 'final',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_RELEASE, specialistskincustomizationtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'lkgtests',
                testDefinition: 'lkgtests',
                runBilbo: false,
                needGameServer: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV, lkgtests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'smoketests',
                testDefinition: 'smoketests',
                runBilbo: false,
                needGameServer: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, smoketests),
                ],
                isManual: true,
            ),
            new AutotestCategory(
                name: 'smoketests_performance',
                testDefinition: 'smoketests_performance',
                runBilbo: false,
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                isTestWithLooseFiles: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, smoketests_performance),
                ],
                isManual: true,
            ),
        ]
    }

    private final List<Platform> defaultPlatforms = [
        new Platform(name: Name.WIN64),
        new Platform(name: Name.PS4),
        new Platform(name: Name.XB1),
    ]

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [
            (DEFAULT)           : defaultPlatforms,
            (KIN_DEV)           : defaultPlatforms,
            (KIN_DEV_UNVERIFIED): defaultPlatforms,
            (KIN_RELEASE)       : defaultPlatforms,
        ]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return getSetting(branchName, runLevelsInParallel)
    }

    @Override
    Map getSlackSettings(String branchName) {
        return (Map) getSetting(branchName, collectingSlackSettings)
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }
}
