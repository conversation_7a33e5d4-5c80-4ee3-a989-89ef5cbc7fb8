package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class DunAutotestMatrix extends AutotestMatrix {

    private static final String DEV_NA_BATTLEFIELDGAME = 'dev-na-battlefieldgame'
    private static final String DEV_NA_BATTLEFIELDGAME_ASAN = 'dev-na-battlefieldgame-asan'
    private static final String DEV_2024_1_DEV_BF = '2024_1_dev-bf'

    // Some default values that can be used across lots of tests
    private static final List EXTRA_ARGS_V1 = ['--rest-port', '5143', '--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true']
    private static final List EXTRA_ARGS_V2 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true']
    private static final List EXTRA_ARGS_V6 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true']
    private static final List EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING = [
        '--use-remote-file-share-handling', true,
        '--use-force-file-share-cleanup', true,
    ]
    /*
    Define sets of tests to run for different types of autotests.
    Add more setups here if needed.
    */
    // Setup for tasks streams

    private static final List<Platform> REGIONAL_BCT_PLATFORMS = [
        new Platform(name: Name.PS5, region: Region.DEV),
        new Platform(name: Name.WIN64, region: Region.WW),
        new Platform(name: Name.XBSX, region: Region.WW),
    ]

    private final TestInfo lkg_qv = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        tests: [
            new TestSuite(
                name: 'LKG_QV',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.WIN64), new Platform(name: Name.XBSX), new Platform(name: Name.PS5)]
            ),
            new TestSuite(
                name: 'LKG_AutoPlayers',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]
            ),
        ]
    )

    private final TestInfo lkg_auto = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        tests: [
            new TestSuite(
                name: 'LKG_CoreCombat',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]
            ),
            new TestSuite(
                name: 'LKG_MMT',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]
            ),
            new TestSuite(
                name: 'LKG_F2P',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]
            ),
            new TestSuite(
                name: 'LKG_SP',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]
            ),
        ]
    )

    private final TestInfo lkg_auto_windows = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        tests: [
            new TestSuite(
                name: 'LKG_GIBS',
                extraArgs: EXTRA_ARGS_V1,
                platforms: [new Platform(name: Name.WIN64)]
            ),
        ]
    )

    private final TestInfo frostedtests_workflow_frostbite = new TestInfo(
        testGroup: 'frostedtests_workflow_frostbite',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frosted_scene_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 360, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_assetGraph_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 360, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_efw_core_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true -sourceControl false"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 360, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_vfx_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 360, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_sourceGeoMesh_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_fbscript_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'fbapi_integration_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
        ]
    )

    private final TestInfo frostedtests_workflow_battlefield = new TestInfo(
        testGroup: 'frostedtests_workflow_battlefield',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frosted_bf_character_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_cinematics_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_hardwareweapons_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_ui_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_hardware_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_techart_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
            new TestSuite(
                name: 'frosted_bf_techdesigncore_workflow_tests',
                extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 180, '--fbapi-test-running-timeout-seconds', 1800],
                poolType: ''
            ),
        ]
    )

    private final TestInfo pt_stab = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'F2P_Stab_Gameplay', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'MP_Stab_Gameplay', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo pt_func = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'A2B_Vehicles',
            'CC_Func_Main',
            'META_Func_GameFlow',
            'META_Func_MyJourney',
            'SP_Func_Playthroughs',
            'SP_Func_Cinematics',
            'A2B_F2P_CoreModes',
            'A2B_WeaponsGadgets',
            'A2B_Attachments',
            'A2B_F2P_Gadgets',
            'A2B_F2P_Weapons',
            'A2B_F2P_Vehicles',
            'A2B_F2P_Gameplay',
            'A2B_F2P_Gameplay',
            'A2B_MMT_GameModes',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_validation = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        tests: [
            new TestSuite(name: 'Systemic_PlaytestValidation', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo pt_func_mutators = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_func_portal',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        tests: [
            new TestSuite(name: 'F2P_Func_Mutators_1', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_Mutators_2', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_Mutators_3', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_Mutators_4', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_ModbuilderLibrary_Basic', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_ModbuilderLibrary_Soldier', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_ModbuilderLibrary_SpatialEditor', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_ModbuilderLibrary_Unique', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'F2P_Func_SpatialEditor', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo lkg_bootanddeploy = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: [EXTRA_ARGS_V1, '--max-parallel-tests', 2],
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
    )

    private final TestInfo lkg_bootanddeploy_tool = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
    )

    private final TestInfo release_setup_dev_na = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 1,
        tests: [
            new TestSuite(name: 'BFUIElements', extraArgs: EXTRA_ARGS_V2, needGameServer: false),
        ]
    )

    private final TestInfo lkg_checkmate = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: EXTRA_ARGS_V1, needGameServer: false),
        ]
    )

    private final TestInfo pt_perf_highpriority_performance_setup = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 14,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Features', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_HUD = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),

        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_Detailed', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Mem_Gameplay', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Mem_Gameplay', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Perf_Flythrough_XBSS', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: false),
            new TestSuite(name: 'MP_Perf_Gameplay_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough_XBSS_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_XBSS_trunk_code_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1].flatten(), needGameServer: false),
            new TestSuite(name: 'F2P_Perf_Flythrough_XBSS_Detailed', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo trunk_asan_NA = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'AsanTestSuite', extraArgs: ['--runtime-connect-timeout', 360, '--ps5-run-options', 'flexibleMemory:2048', '--crash-dump-timeout', 6000, '--timeout-client-level-load', '01:00:00', '--timeout-server-level-load', '01:00:00', '--level-load-timeout', 600, '--timeout-client-start', '00:30:00']),
        ]
    )

    private final TestInfo trunk_asan_ps5_NA = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'AsanTestSuite', extraArgs: ['--runtime-connect-timeout', 360, '--max-parallel-tests', 1, '--ps5-run-options', 'flexibleMemory:2048', '--crash-dump-timeout', 6000, '--timeout-client-level-load', '01:00:00', '--timeout-server-level-load', '01:00:00', '--level-load-timeout', 600, '--timeout-client-start', '00:30:00', '--timeout-client-deploy', '01:10:00']),
        ]
    )

    private final TestInfo pt_perf_flythroughs_XBSS_trunk_code_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Perf_Flythrough_Detailed_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1].flatten(), needGameServer: false),
            new TestSuite(name: 'MP_Mem_Gameplay_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_XBSS_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 20,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_XBSS_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 20,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 24,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_MinSpec_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_minspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 24,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Gameplay_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(), needGameServer: false),
            new TestSuite(name: 'F2P_Perf_Features_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo unittests_engine = new TestInfo(
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'EngineUnitTests', extraArgs: [EXTRA_ARGS_V2, '--max-parallel-tests', 1].flatten(), poolType: ''),
        ]
    )

    private final TestInfo unittests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AutoPlayersCodeTests', extraArgs: [EXTRA_ARGS_V2, '--max-parallel-tests', 1].flatten()),
        ]
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 24,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_MinSpec_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Mem_Gameplay_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo pt_perf_recspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 24,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Gameplay_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Gameplay_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_recspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 16,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_Detailed_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough_RecSpec_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Mem_Gameplay_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Mem_Gameplay_RecSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup3 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 4,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Perf_Flythrough_Detailed_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false),
            new TestSuite(name: 'F2P_Mem_Gameplay_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo pt_perf_lowpriority_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_Detailed', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough:F2P_Perf_Flythrough_ZoneTestVTAR_Jeep', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay', timeoutHours: 3, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'Common_Perf_CoreCombat', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Flythrough', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )
    /***********************************************
     Specific pslice and zonetest2 perf/mem tests for 2024_1_dev-bf
     */

    private final TestInfo pslice_mem_mp_final = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay:MemoryPlayTest_Pslice_Conquest0', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_mem_sp_final = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Mem_Gameplay:SP_Playthrough_PSlice', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_mem_and_perfmimic_final_recspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_Pslice_Conquest0_gla_rec_spec_Fidelity:MemoryPlayTest_Pslice_Conquest0_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_mem_final_minspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_Pslice_Conquest0_gla_min_spec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_perf_performance = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Gameplay:AutoPlayTest_Pslice_Conquest0_Fidelity:AutoPlayTest_Pslice_Conquest0_Performance', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough:Flythrough_SP_Pslice_Zone1', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough:Flythrough_Pslice', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay:SP_PSlice_ICG_Fidelity:SP_PSlice_ICG_Performance:SP_Playthrough_PSlice_Fidelity:SP_Playthrough_PSlice_Performance', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_perf_performance_hud = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )
/*
    private final TestInfo pslice_perf_performance_recspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Gameplay_RecSpec:AutoPlayTest_Pslice_Conquest0_gla_rec_spec_Fidelity:AutoPlayTest_Pslice_Conquest0_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay_RecSpec:SP_PSlice_ICG_gla_rec_spec_Fidelity:SP_PSlice_ICG_gla_rec_spec_Performance:SP_Playthrough_PSlice_gla_rec_spec_Fidelity:SP_Playthrough_PSlice_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_RecSpec:Flythrough_SP_PSlice_Zone1_gla_rec_spec_Performance:Flythrough_SP_Pslice_Zone1_gla_rec_spec_Fidelity', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_RecSpec:Flythrough_Pslice_gla_rec_spec_Fidelity:Flythrough_Pslice_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )*/

    private final TestInfo pslice_perf_performance_minspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Gameplay_MinSpec:AutoPlayTest_Pslice_Conquest0_gla_min_spec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Gameplay_MinSpec:SP_PSlice_ICG_gla_min_spec:SP_Playthrough_PSlice_gla_min_spec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_MinSpec:Flythrough_Pslice_gla_min_spec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo pslice_perf_performance_minspec_noserver = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Perf_Flythrough_MinSpec:Flythrough_SP_Pslice_Zone1_gla_min_spec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(), needGameServer: false),
        ],
    )

    private final TestInfo zontest2_mem_final_recspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_RecSpec:F2P_Mem_Gameplay_RecSpec_MPZoneTest2_GraniteSquad0_gla_rec_spec_Fidelity:F2P_Mem_Gameplay_RecSpec_MPZoneTest2_GraniteSquad0_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo zontest2_mem_final_minspec = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_MinSpec:F2P_Mem_Gameplay_MinSpec_MPZoneTest2_GraniteSquad0_gla_min_spec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo zontest2_perf_performance = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough:F2P_Perf_Flythrough_ZoneTest2_Engineer:F2P_Perf_Flythrough_ZoneTest2_Port', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--max-parallel-tests', 1, '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo zontest2_perf_performance_recspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_RecSpec:F2P_Perf_Flythrough_RecSpec_ZoneTest2_Engineer_gla_rec_spec_Performance:F2P_Perf_Flythrough_RecSpec_ZoneTest2_Port_gla_rec_spec_Performance', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )

    private final TestInfo zontest2_perf_performance_minspec = new TestInfo(
        remoteLabel: 'eala',
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_MinSpec:F2P_Perf_Flythrough_MinSpec_ZoneTest2_Engineer_gla_min_spec:F2P_Perf_Flythrough_MinSpec_ZoneTest2_Port_gla_min_spec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ],
    )

    /***********************************************
     */
    private final Map collectingSlackSettings = [
        (DEFAULT)                    : [
            channels                  : [],
            skip_for_multiple_failures: true
        ],
        (DEV_NA_BATTLEFIELDGAME)     : [
            channels                  : ['#bf-dev-na-build-notify', '#clockwork-notify'],
            skip_for_multiple_failures: true,
        ],
        (DEV_NA_BATTLEFIELDGAME_ASAN): [
            channels                  : ['#bf-dev-na-build-notify', '#clockwork-notify'],
            skip_for_multiple_failures: true,
        ],
        (DEV_2024_1_DEV_BF)          : [
            channels                  : ['#bf-dev-na-build-notify', '#clockwork-notify'],
            skip_for_multiple_failures: true,
        ],
    ]

    /* ********** ********** ********** ********** **********
    Define if the levels should be tested in parallel or not.
    */
    private final Map runLevelsInParallel = [
        (DEFAULT)                    : true,
        (DEV_NA_BATTLEFIELDGAME)     : false,
        (DEV_NA_BATTLEFIELDGAME_ASAN): false,
        (DEV_2024_1_DEV_BF)          : false,
    ]

    @Override
    List<String> getBranches() {
        return [DEV_NA_BATTLEFIELDGAME, DEV_NA_BATTLEFIELDGAME_ASAN, DEV_2024_1_DEV_BF]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return [
            new AutotestCategory(
                name: 'lkg_checkmate',
                testDefinition: 'lkg_checkmate',
                enableP4Counters: true,
                buildType: 'dll',
                config: 'release',
                runBilbo: true,
                needGameServer: false,
                uploadJournals: true,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_checkmate, 'H * * * 1-6\nH 6-23 * * 7'),
                    branchConfiguration(DEV_2024_1_DEV_BF, lkg_checkmate, 'H * * * 1-6\nH 6-23 * * 7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_auto',
                testDefinition: 'lkg_auto',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: true,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_auto, 'H */4 * * 1-7'),
                    branchConfiguration(DEV_2024_1_DEV_BF, lkg_auto, 'H 3,5,20,22 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_auto_windows',
                testDefinition: 'lkg_auto_windows',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: true,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_auto_windows, 'H */4 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_qv',
                testDefinition: 'lkg_qv',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_qv, 'H */3 * * 1-6\nH 6-23 * * 7'),
                    branchConfiguration(DEV_2024_1_DEV_BF, lkg_qv, 'H 7,9,16,21 * * 1-6\nH 7,9,16,21 * * 7'),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_workflow_frostbite',
                testDefinition: 'frostedtests_workflow_frostbite',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, frostedtests_workflow_frostbite, 'H 8,16 * * 1-7'),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_workflow_frostbite_eala',
                testDefinition: 'frostedtests_workflow_frostbite',
                remoteLabel: 'eala',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, frostedtests_workflow_frostbite, 'H 9,19 * * 1-7'),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_workflow_battlefield',
                testDefinition: 'frostedtests_workflow_battlefield',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, frostedtests_workflow_battlefield, 'H 11,21 * * 1-7'),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_workflow_battlefield_eala',
                testDefinition: 'frostedtests_workflow_battlefield_eala',
                runBilbo: false,
                remoteLabel: 'eala',
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, frostedtests_workflow_battlefield, 'H 9,19 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_func',
                testDefinition: 'pt_func',
                runBilbo: true,
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_func, 'H 7 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'trunk_asan_NA',
                testDefinition: 'trunk_asan_NA',
                runBilbo: true,
                needGameServer: false,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME_ASAN, trunk_asan_NA, '* 6 * * 1-6')
                ]
            ),
            new AutotestCategory(
                name: 'trunk_asan_ps5_NA',
                testDefinition: 'trunk_asan_ps5_NA',
                runBilbo: true,
                needGameServer: false,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME_ASAN, trunk_asan_ps5_NA, '* 8 * * 1-6')
                ]
            ),
            new AutotestCategory(
                name: 'pt_func_validation',
                testDefinition: 'pt_func_validation',
                runBilbo: true,
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_func_validation, 'H 8 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_func_mutators',
                testDefinition: 'pt_func_mutators',
                runBilbo: true,
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_func_mutators, 'H 15 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'release_setup_dev_na',
                testDefinition: 'release_setup_dev_na',
                runBilbo: true,
                needGameServer: false,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, release_setup_dev_na, 'H 10 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_stab',
                testDefinition: 'pt_stab',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_stab, 'H 4,22 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_highpriority_performance_setup',
                testDefinition: 'pt_perf_highpriority_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_highpriority_performance_setup, 'H 0,15 * * 1-6'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_highpriority_flythroughs_final_setup',
                testDefinition: 'pt_perf_highpriority_flythroughs_final_setup',
                remoteLabel: 'eala',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_highpriority_flythroughs_final_setup, 'H 11,16 * * 1-6'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_HUD',
                testDefinition: 'pt_perf_HUD',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_HUD, 'H 16 * * 1'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_bootanddeploy',
                testDefinition: 'lkg_bootanddeploy',
                enableP4Counters: true,
                runBilbo: true,
                uploadJournals: true,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_bootanddeploy, 'H 22,3 * * 1-7'),
                    branchConfiguration(DEV_2024_1_DEV_BF, lkg_bootanddeploy, 'H 15,23 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_bootanddeploy_tool',
                testDefinition: 'lkg_bootanddeploy_tool',
                enableP4Counters: true,
                runBilbo: true,
                buildType: 'dll',
                config: 'release',
                uploadJournals: true,
                captureVideo: true,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_bootanddeploy_tool, 'H 3,6,9,12,15,18,21,0 * * 1-7'),
                    branchConfiguration(DEV_2024_1_DEV_BF, lkg_bootanddeploy_tool, 'H 4,7,10,13,16,19,22,1 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_highpriority_flythroughs_final_setup2',
                testDefinition: 'pt_perf_highpriority_flythroughs_final_setup2',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_highpriority_flythroughs_final_setup2, 'H 19 * * 1-6'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_final_setup',
                testDefinition: 'pt_perf_flythroughs_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_final_setup, 'H 4 * * 1-6'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_performance_setup',
                testDefinition: 'pt_perf_lowpriority_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_lowpriority_performance_setup, 'H 0 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_minspec_final_setup3',
                testDefinition: 'pt_perf_flythroughs_minspec_final_setup3',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_minspec_final_setup3, 'H 15 * * 7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_minspec_performance_setup',
                testDefinition: 'pt_perf_minspec_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_minspec_performance_setup, 'H 4 * * 7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_minspec_final_setup',
                testDefinition: 'pt_perf_flythroughs_minspec_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_minspec_final_setup, 'H 16 * * 7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_minspec_final_setup2',
                testDefinition: 'pt_perf_flythroughs_minspec_final_setup2',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_minspec_final_setup2, 'H 8 * * 7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_recspec_performance_setup',
                testDefinition: 'pt_perf_recspec_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_recspec_performance_setup, 'H 12 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_recspec_final_setup',
                testDefinition: 'pt_perf_flythroughs_recspec_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_recspec_final_setup, 'H H/12 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_final_setup',
                testDefinition: 'pt_perf_lowpriority_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_lowpriority_final_setup, 'H 9 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_XBSS_performance_setup',
                testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_XBSS_performance_setup, 'H 0 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_XBSS_trunk_code_final_setup',
                testDefinition: 'pt_perf_flythroughs_XBSS_trunk_code_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_XBSS_trunk_code_final_setup, 'H 7 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_XBSS_trunk_code_final_setup2',
                testDefinition: 'pt_perf_flythroughs_XBSS_trunk_code_final_setup2',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_flythroughs_XBSS_trunk_code_final_setup2, 'H 13 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_XBSS_performance_setup',
                testDefinition: 'pt_perf_lowpriority_XBSS_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_lowpriority_XBSS_performance_setup, 'H 0 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_XBSS_final_setup',
                testDefinition: 'pt_perf_lowpriority_XBSS_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_lowpriority_XBSS_final_setup, 'H 18 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_final_setup_synced',
                testDefinition: 'pt_perf_lowpriority_final_setup_synced',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, pt_perf_lowpriority_final_setup_synced, 'H 20 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_engine',
                testDefinition: 'unittests_engine',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, unittests_engine, 'H 2,10,18 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_engine_eala',
                testDefinition: 'unittests_engine',
                remoteLabel: 'eala',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, unittests_engine, 'H 4,12,20 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests',
                testDefinition: 'unittests',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                jobLabel: 'poolbuild',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, unittests, 'H 0,8,16 * * 1-7'),

                ]
            ),
            new AutotestCategory(
                name: 'unittests_eala',
                testDefinition: 'unittests',
                remoteLabel: 'eala',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, unittests, 'H 6,14,19 * * 1-7'),

                ]
            ),
            new AutotestCategory(
                name: 'pslice_mem_mp_final',
                testDefinition: 'pslice_mem_mp_final',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_mem_mp_final, 'H 21 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_mem_sp_final',
                testDefinition: 'pslice_mem_sp_final',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_mem_sp_final, 'H 11 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_mem_and_perfmimic_final_recspec',
                testDefinition: 'pslice_mem_and_perfmimic_final_recspec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_mem_and_perfmimic_final_recspec, 'H 19 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_mem_final_minspec',
                testDefinition: 'pslice_mem_final_minspec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_mem_final_minspec, 'H 3 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_perf_performance',
                testDefinition: 'pslice_perf_performance',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_perf_performance, 'H 0 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_perf_performance_hud',
                testDefinition: 'pslice_perf_performance_hud',
                remoteLabel: 'eala',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_perf_performance_hud, 'H 13 * * 1-6'),
                ]),/*
            new AutotestCategory(
                name: 'pslice_perf_performance_recspec',
                testDefinition: 'pslice_perf_performance_recspec',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_perf_performance_recspec, 'H 7 * * 1-6'),
                ]),*/
            new AutotestCategory(
                name: 'pslice_perf_performance_minspec',
                testDefinition: 'pslice_perf_performance_minspec',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_perf_performance_minspec, 'H 17 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pslice_perf_performance_minspec_noserver',
                testDefinition: 'pslice_perf_performance_minspec_noserver',
                runBilbo: true,
                needGameServer: false,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, pslice_perf_performance_minspec_noserver, 'H 15 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'zontest2_mem_final_recspec',
                testDefinition: 'zontest2_mem_final_recspec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, zontest2_mem_final_recspec, 'H 18 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'zontest2_mem_final_minspec',
                testDefinition: 'zontest2_mem_final_minspec',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, zontest2_mem_final_minspec, 'H 13 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'zontest2_perf_performance',
                testDefinition: 'zontest2_perf_performance',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, zontest2_perf_performance, 'H 21 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'zontest2_perf_performance_recspec',
                testDefinition: 'zontest2_perf_performance_recspec',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, zontest2_perf_performance_recspec, 'H 16 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'zontest2_perf_performance_minspec',
                testDefinition: 'zontest2_perf_performance_minspec',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(DEV_2024_1_DEV_BF, zontest2_perf_performance_minspec, 'H 9 * * 1-6'),
                ]),
        ]
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return [
            new AutotestCategory(
                name: 'lkg_auto',
                testDefinition: 'lkg_auto',
                runBilbo: false,
                needGameServer: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, lkg_auto, ''),
                ],
                isManual: true,
                jobLabel: 'poolbuild',
            ),
        ]
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [
            (DEFAULT): [
                new Platform(name: Name.WIN64),
                new Platform(name: Name.PS5),
                new Platform(name: Name.XBSX)
            ],
        ]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return getSetting(branchName, runLevelsInParallel)
    }

    @Override
    Map getSlackSettings(String branchName) {
        return (Map) getSetting(branchName, collectingSlackSettings)
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }
}
