"""
test_backup_baseline.py

Unit testing for backup_baseline
"""
import unittest
from unittest.mock import patch, MagicMock

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from dice_elipy_scripts.backup_baseline import cli
from dice_elipy_scripts.backup_baseline import copy_baseline_build
from elipy2.exceptions import ELIPYException


class TestBackupBaseline(unittest.TestCase):
    def setUp(self):
        self.platform = "ps5"
        self.source = "c:\\drive_that_doesnt_exist"
        self.target = "c:\\target"

    @patch("dice_elipy_scripts.backup_baseline.file_check")
    def test_copy_baseline_build_raises_ELIPYException(self, mock_file_check):
        # Given failing file_check invocation
        mock_file_check.side_effect = ELIPYException
        # When copy_baseline_build is called
        with self.assertRaises(ELIPYException):
            # Then an ELIPYException is raised
            copy_baseline_build(self.source, self.target)

    @patch("dice_elipy_scripts.backup_baseline.file_check")
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy")
    def test_copy_baseline_build_calls_robocopy(
        self,
        mock_robocopy,
        mock_file_check,
    ):
        # Given non failing file_check
        mock_file_check.side_effect = None
        # When copy_baseline_build is called
        copy_baseline_build(self.source, self.target)
        # robocopy is called with the correct source and target
        mock_robocopy.assert_called_once_with(self.source, self.target)

    @patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils")
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path", MagicMock())
    @patch("elipy2.filer_paths.get_baseline_build_path", MagicMock())
    def test_metadata_manager_created_on_successful_copy_baseline_build(
        self,
        mock_setup_metadata_manager,
    ):
        # Given non failing os.path.join and copy_baseline_build

        # When the backup_baseline cli is executed
        test_args = [
            "--platform",
            "ps5",
            "--baseline-data-branch",
            "data_branch",
            "--baseline-data-changelist",
            "4381933",
            "--baseline-code-branch",
            "code_branch",
            "--baseline-code-changelist",
            "11154536",
        ]
        runner = CliRunner()
        runner.invoke(cli, test_args)

        # a setup_metadata_manager is instantiated without args
        mock_setup_metadata_manager.setup_metadata_manager.assert_called_once_with()
