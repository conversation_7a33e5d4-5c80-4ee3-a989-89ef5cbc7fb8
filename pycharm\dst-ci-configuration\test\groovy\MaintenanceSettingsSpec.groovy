import com.ea.lib.jobsettings.MaintenanceSettings
import spock.lang.Specification

class MaintenanceSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name              : 'branch',
            workspace_root           : 'workspace-root',
            azure_workspace_root     : 'azure_workspace-root',
            dataset                  : 'dataset',
            disable_build_maintenance: true,
            elipy_call               : 'elipy-call',
            azure_elipy_call         : 'azure_elipy-call',
            elipy_install_call       : 'elipy-install-call',
            azure_elipy_install_call : 'azure_elipy-install-call',
            p4_delete_workspace_label: 'label',
            scheduled_nuke           : '1 2 3 4 5',
            nuke_label               : 'de_nuke',
            dropdb_label             : 'de_nuke2',
            scheduled_dropdb         : '1 2 3 4 5',
            master_restart_timer     : '0 0 * * 4',
            wait_doquite_hours       : 12,
            wait_forcekill_hours     : 3,
            restart_nodes            : false,
            restart_controller       : false,
            job_label_vault          : 'job_label_vault',
            extra_vault_args         : ['--no-win64-trial', '--no-server-trial'],
            p4_fb_settings           : [p4_creds: 'p4-creds', p4_port: '1111'],
            p4_code_root             : '/',
            p4_code_creds            : 'code-perforce',
            p4_code_client           : 'jenkins',
            p4_data_creds            : 'data-perforce',
            p4_data_root             : '//data/',
            p4_data_client           : 'jenkins-stream',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map maintenance_branch = [branch: [data_branch: 'data-branch', data_folder: 'data', code_branch: 'code-branch', code_folder: 'code']]
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static String p4_code_client_env = 'p4-code-client-env'
        static String p4_data_client_env = 'p4-data-client-env'
        static String p4_user_single_slash = 'p4-user-single-slash'
        static String p4_code_server = 'p4-code-server'
        static String p4_data_server = 'p4-data-server'
        static String dataset = 'project-dataset'
        static Boolean vault_win64_trial = true
        static Boolean verify_post_vault = true
        static String p4_browser_url = 'localhost'
        static String short_name = 'kin'
    }

    void "test that we get expected job settings in initializeAvalancheMaintenanceStart"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheMaintenanceStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            isDisabled == BranchFile.standard_jobs_settings.disable_build_maintenance
            description == 'Schedule Avalanche maintenance for all nodes on this master.'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeAvalancheMaintenanceJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheMaintenanceJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Performs Avalanche maintenance on the build node.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanche_maintenance"
        }
    }

    void "test that we get expected job settings in initializeAvalancheMaintenanceAzureJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheMaintenanceAzureJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Performs Avalanche maintenance on the build node.'
            workspaceRoot == BranchFile.standard_jobs_settings.azure_workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${BranchFile.standard_jobs_settings.azure_elipy_call} avalanche_maintenance"
            elipyInstallCall == BranchFile.standard_jobs_settings.azure_elipy_install_call
        }
    }

    void "test that we get expected job settings in initializeP4CleanCodestreamJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4CleanCodestreamJob(ProjectFile, BranchFile.standard_jobs_settings, 'port')
        then:
        with(settings) {
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            elipyInstallCall == BranchFile.standard_jobs_settings.elipy_install_call
            elipyCall == BranchFile.standard_jobs_settings.elipy_call
            description == 'Performs p4 clean on the build node codestream on port.'
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${elipyCall} p4_clean code port ${ProjectFile.p4_code_client_env} ${ProjectFile.p4_user_single_slash}"
        }
    }

    void "test that we get expected job settings in initializeP4CleanDatastreamJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4CleanDatastreamJob(ProjectFile, BranchFile.standard_jobs_settings, 'port', 'dataset')
        then:
        with(settings) {
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            elipyInstallCall == BranchFile.standard_jobs_settings.elipy_install_call
            elipyCall == BranchFile.standard_jobs_settings.elipy_call
            description == 'Performs p4 clean on the build node datastream on port using dataset dataset.'
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${elipyCall} p4_clean data port ${ProjectFile.p4_data_client_env} ${ProjectFile.p4_user_single_slash} dataset"
        }
    }

    void "test that we get expected job settings in initializeP4DeleteWorkspace"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4DeleteWorkspace(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Remove workspace from perforce server.'
            jobLabel == 'label'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${BUILD_NUMBER}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} p4_delete_workspace" +
                " --codeport ${ProjectFile.p4_code_server}" +
                " --codeclient ${ProjectFile.p4_code_client_env}" +
                " --dataport ${ProjectFile.p4_data_server}" +
                " --dataclient ${ProjectFile.p4_data_client_env}" +
                " --user ${ProjectFile.p4_user_single_slash}" +
                ' --codeworkspace jenkins-%node%-codestream' +
                " --dataworkspace jenkins-%node%-${ProjectFile.dataset}stream"
        }
    }

    void "test that we get expected job settings in initializeAvalancheCliNukeStart"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'running avalanchecli nuke on build machines, could select more than one machine at the same time.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${machine}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanchecli_nuke --datadir ${BranchFile.standard_jobs_settings.dataset}"
        }
    }

    void "test that we get expected job settings in initializeAvalancheCliNukeAll"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeAll(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'running avalanchecli nuke on build machines, could select more than one machine at the same time.'
            cronTrigger == BranchFile.standard_jobs_settings.scheduled_nuke
            allMachineLabel == BranchFile.standard_jobs_settings.nuke_label
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${machine}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanchecli_nuke --datadir ${BranchFile.standard_jobs_settings.dataset}"
        }
    }

    void "test that we get expected job settings in initializeAvalancheCliNukeJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Performs an avalanchecli nuke -y.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanchecli_nuke --datadir ${BranchFile.standard_jobs_settings.dataset}"
        }
    }

    void "test that we get expected job settings in initializeAvalancheCliDropAllDbsStart"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliDropAllDbsStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Drops all Avalanche databases on build machines, it is possible to select more than one machine at a time.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            allMachineLabel == BranchFile.standard_jobs_settings.dropdb_label
            cronTrigger == BranchFile.standard_jobs_settings.scheduled_dropdb
            buildName == '${JOB_NAME}.${machine}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanchecli_drop_all_dbs"
        }
    }

    void "test that we get expected job settings in initializeAvalancheCliDropAllDbsJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliDropAllDbsJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Drop all avalanche dbs on localhost'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} avalanchecli_drop_all_dbs"
        }
    }

    void "test that we get expected job settings in initializeCleanAgentFolder"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeCleanAgentFolder(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Run a cleaning script against the selected agents'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${machine}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} clean_agent" +
                ' --delete-packages %delete_packages%' +
                ' --delete-packagesdev %delete_packagesdev%' +
                ' --delete-pip-cache %delete_pip_cache%' +
                ' --delete-logs %delete_logs%' +
                ' --delete-tnt-local %delete_tnt_local%' +
                ' --delete-localpackages %delete_local_packages%' +
                ' --delete-temp %delete_temp%' +
                ' --delete-data-state %delete_data_state%' +
                " --data-dir ${BranchFile.standard_jobs_settings.dataset}"
            parameters.each { it.value.flag.class == boolean && description }
        }
    }

    void "test that we get expected job settings in initializeJenkinsShutdownJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeJenkinsShutdownJob(BranchFile.standard_jobs_settings, true)
        then:
        with(settings) {
            description == 'Shutdown the Jenkins controller when no jobs are running --> the supervisor will restart a fresh container.'
            cronTrigger == BranchFile.standard_jobs_settings.master_restart_timer
            waitHours == BranchFile.standard_jobs_settings.wait_doquite_hours as String
            forceWaitHours == BranchFile.standard_jobs_settings.wait_forcekill_hours as String
            restartNodes == BranchFile.standard_jobs_settings.restart_nodes
            restartController == BranchFile.standard_jobs_settings.restart_controller

            hasCronTrigger == true
        }
    }

    void "test that we get expected job settings in initializeSyncStart"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeSyncStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Triggers a sync on all prepare machines'
            cronTrigger == 'H/30 * * * 1-6\nH/30 6-23 * * 7'
            jobLabel == 'prepare-branch'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeSyncJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeSyncJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Syncs a machine to branch code and data.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            nodeNameDefaultValue == 'prepare-branch'
            buildName == '${JOB_NAME}.${NODE_NAME}.${P4_CHANGELIST}'
            batchScript == "echo 'Done'"
        }
    }

    void "test that we get expected job settings in initializeCodeWarmJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeCodeWarmJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            p4Data.codeCreds == BranchFile.standard_jobs_settings.p4_code_creds
            p4Data.codeRoot == BranchFile.standard_jobs_settings.p4_code_root
            p4Data.codeClient == BranchFile.standard_jobs_settings.p4_code_client
            p4Data.browserUrl == ProjectFile.p4_browser_url
            codeBranch == 'code-branch'
            codeFolder == 'code'
            timeoutMinutes == 1440
            description == 'Code warms a machine.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}.${code_changelist}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} codebuild %platform% %config%" +
                ' --code-branch %branch% --code-changelist %code_changelist% --clean %clean_local%' +
                " --p4-port %P4_PORT% --p4-client ${ProjectFile.p4_code_client_env} --p4-user %P4_USER% --dry-run ${settings.extraArgs}"
        }
    }

    @SuppressWarnings('UnnecessaryObjectReferences')
    void "test that we get expected job settings in initializeDataWarmJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeDataWarmJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            p4Data.codeCreds == BranchFile.standard_jobs_settings.p4_code_creds
            p4Data.codeRoot == BranchFile.standard_jobs_settings.p4_code_root
            p4Data.codeClient == BranchFile.standard_jobs_settings.p4_code_client
            p4Data.dataCreds == BranchFile.standard_jobs_settings.p4_data_creds
            p4Data.dataRoot == BranchFile.standard_jobs_settings.p4_data_root
            p4Data.dataClient == BranchFile.standard_jobs_settings.p4_data_client
            p4Data.browserUrl == ProjectFile.p4_browser_url
            codeBranch == 'code-branch'
            codeFolder == 'code'
            dataBranch == 'data-branch'
            dataFolder == 'data'
            timeoutMinutes == 1440
            description == 'Data warms a machine.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${NODE_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} databuild %dataset% %platform% %asset%" +
                ' --code-branch %code_branch% --code-changelist %code_changelist%' +
                ' --data-branch %data_branch% --data-changelist %data_changelist%'
        }
    }

    void "test that we get expected job settings in initializeVaultStart"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeVaultStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Triggers vaulting for build and symbols.'
            projectName == ProjectFile.name
            vaultWin64Trial == ProjectFile.vault_win64_trial
            verifyPostVault == ProjectFile.verify_post_vault
        }
    }

    void "test that we get expected job settings in initializeVaultJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeVaultJob(BranchFile, MasterFile, ProjectFile, 'branch', 'jobType')
        then:
        with(settings) {
            description == 'Vaults jobType.'
            extraArgs == '--no-win64-trial --no-server-trial'
            jobLabel == 'job_label_vault'
            fbLoginDetails == BranchFile.standard_jobs_settings.p4_fb_settings
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="platform"}'
            fbLoginDetailsCall == 'echo %fb_p4_passwd%|p4 -p 1111 -u %fb_p4_user% login & exit 0'
            downstreamTrigger == "store.baseline.${ProjectFile.short_name}.build"
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} vault --vault-type symbols --platform %platform%" +
                ' --code-branch %code_branch% --code-changelist %code_changelist%' +
                ' --data-branch %data_branch% --data-changelist %data_changelist%' +
                ' --build-url %BUILD_URL% --build-id %BUILD_NUMBER% --version "%version%"' +
                ' %vault_win64_trial% --md5-validation --build-location "%build_location%"' +
                ' --vault-verification-location "%vault_verification_location%"' +
                " ${extraArgs}"
            elipyCmd2 == "${BranchFile.standard_jobs_settings.elipy_call} vault --vault-type jobType --platform %platform%" +
                ' --code-branch %code_branch% --code-changelist %code_changelist%' +
                ' --data-branch %data_branch% --data-changelist %data_changelist%' +
                ' --build-url %BUILD_URL% --build-id %BUILD_NUMBER% --version "%version%"' +
                ' --md5-validation %expression_debug_data% %verify_post_vault% --build-location "%build_location%"' +
                ' --vault-verification-location "%vault_verification_location%"' +
                " ${extraArgs}"
        }
    }

    void "test that we get expected job settings in initializeBaselineJob"() {
        when:
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeBaselineJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            jobLabel == 'job_label_vault'
            description == 'Stores the baseline.'
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="platform"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} backup_baseline --platform %platform%" +
                ' --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist%' +
                ' --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%' +
                ' --build-location "%build_location%" --additional-baseline-locations "%additional_baseline_locations%"'
        }
    }
}
