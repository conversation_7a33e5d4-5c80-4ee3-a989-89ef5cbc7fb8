package dicebuildjenkins

import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibBuildDeleter
import com.ea.lib.jobs.LibMaintenance
import com.ea.project.bct.Bct
import com.ea.project.fb1.Fb1Battlefieldgame
import com.ea.project.gnt.Granite
import com.ea.project.kin.Kingston
import com.ea.project.mer.Merlin
import com.ea.project.nfs.NFSUpgrade

def build_deleter_projects = [
    'Kingston'                    : [
        project_name : Kingston,
        branch_folder: 'dev',
        branch_name  : 'kin-dev',
        extra_args   : '--include-path-retention --exclude expressiondebugdata\\BattlefieldGame',
    ],
    'Kingston-EALA'               : [
        project_name  : Kingston,
        branch_folder : 'dev',
        label         : 'kin_eala',
        branch_name   : 'kin-dev',
        extra_args    : '--no-shift-delete',
        elipy_call    : Kingston.elipy_call_eala,
        p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_code_creds : 'fb1-la-p4',
        parallel_jobs : 1,
    ],
    'Kingston-EARO'               : [
        project_name : Kingston,
        branch_folder: 'dev',
        label        : 'earo',
        branch_name  : 'kin-dev',
        extra_args   : '--no-shift-delete --creation-time-deletion',
        elipy_call   : Kingston.elipy_call_earo,
        parallel_jobs: 1,
    ],
    'Kingston-tnt_local-symstores': [
        project_name  : Kingston,
        branch_folder : 'dev',
        branch_name   : 'kin-dev',
        trigger_string: '0 1 * * 6',
        extra_args    : '--no-shift-delete --include tnt_local --clean-symstore-days 60',
        label         : 'Kingston',
    ],
    'Kingston_expressiondebugdata': [
        project_name : Kingston,
        branch_folder: 'dev',
        branch_name  : 'kin-dev',
        extra_args   : '--no-shift-delete --include expressiondebugdata\\BattlefieldGame',
        label        : 'Kingston',
    ],
    'Fb1-EARO'                    : [
        project_name : Fb1Battlefieldgame,
        branch_folder: 'fbstream',
        branch_name  : 'dev',
        label        : 'earo',
        extra_args   : '--no-shift-delete --creation-time-deletion',
        elipy_call   : Fb1Battlefieldgame.elipy_call_earo,
        parallel_jobs: 1,
    ],
    'Fb1-EALA'                    : [
        project_name  : Fb1Battlefieldgame,
        branch_folder : 'fbstream',
        branch_name   : 'dev',
        label         : 'fb1_eala',
        p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_code_creds : 'fb1-la-p4',
        extra_args    : '--no-shift-delete  --include-path-retention',
        elipy_call    : Fb1Battlefieldgame.elipy_call_eala,
    ],
    'Fb1'                         : [
        project_name : Fb1Battlefieldgame,
        branch_folder: 'fbstream',
        branch_name  : 'dev',
        extra_args   : '--no-shift-delete  --include-path-retention',
    ],
    'Fb1-symstore'                : [
        project_name  : Fb1Battlefieldgame,
        branch_folder : 'fbstream',
        branch_name   : 'dev',
        trigger_string: '0 1 * * 6',
        extra_args    : '--no-shift-delete --include tnt_local  --clean-symstore-days 30',
        label         : 'fb1',
    ],
    'Granite'                     : [
        project_name : Granite,
        branch_folder: 'gnt',
        branch_name  : 'gnt-proto',
        extra_args   : '--no-shift-delete --clean-symstore-days 60',
        label        : 'granite',
        disable_build: true,
    ],
    'Bct'                         : [
        project_name : Bct,
        branch_folder: 'mainline',
        branch_name  : 'trunk-code-dev',
        extra_args   : '--no-shift-delete',
        label        : 'bct',
    ],
    'Bct-path-retention'          : [
        project_name : Bct,
        branch_folder: 'mainline',
        branch_name  : 'trunk-code-dev',
        extra_args   : '--no-shift-delete --exclude-retention-categories --include-path-retention',
        label        : 'bct',
    ],
    'Bct-EARO'                    : [
        project_name : Bct,
        branch_folder: 'mainline',
        branch_name  : 'trunk-code-dev',
        extra_args   : '--no-shift-delete --creation-time-deletion',
        label        : 'earo',
        elipy_call   : Bct.elipy_call_earo,
        parallel_jobs: 1,
    ],
    'Bct-Montreal'                : [
        project_name : Bct,
        branch_folder: 'mainline',
        branch_name  : 'trunk-content-dev',
        extra_args   : '--no-shift-delete --creation-time-deletion',
        label        : 'bct_Montreal',
        elipy_call   : Bct.elipy_call_montreal,
        parallel_jobs: 2,
    ],
    'Bct-EALA'                    : [
        project_name  : Bct,
        branch_folder : 'mainline',
        branch_name   : 'trunk-code-dev',
        extra_args    : '--no-shift-delete --creation-time-deletion',
        label         : 'bct_eala',
        p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_code_creds : 'fb1-la-p4',
        elipy_call    : Bct.elipy_call_eala,
    ],
    'Bct-EALA-Outsourcers'        : [
        project_name  : Bct,
        branch_folder : 'mainline',
        branch_name   : 'trunk-code-dev',
        extra_args    : '--no-shift-delete --creation-time-deletion --include-path-retention',
        label         : 'bct_eala',
        p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_code_creds : 'fb1-la-p4',
        elipy_call    : Bct.elipy_call_eala_outsourcers,
    ],
    'Bct-criterion'               : [
        project_name   : Bct,
        branch_folder  : 'mainline',
        branch_name    : 'trunk-code-dev',
        extra_args     : '--include-path-retention',
        label          : 'bct_criterion',
        p4_code_server : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        p4_code_creds  : 'perforce-battlefield-criterion',
        elipy_call     : Bct.elipy_call_criterion,
        no_shift_delete: 'false',
    ],
    'Bct-symstore'                : [
        project_name      : Bct,
        branch_folder     : 'mainline',
        branch_name       : 'trunk-code-dev',
        trigger_string    : '0 1 * * 1-6\n0 6 * * 7',
        extra_args        : '--no-shift-delete --exclude-retention-categories --clean-symstore-days 40',
        label             : 'bct',
        skip_empty_folders: true,
        timeout_hours     : 24,
    ],

]

def outages_metrics_projects = [
    'Fb1'       : [
        project_name      : Fb1Battlefieldgame,
        branch_folder     : 'fbstream',
        branch_name       : 'dev',
        elipy_call        : Fb1Battlefieldgame.elipy_call,
        elipy_install_call: Fb1Battlefieldgame.elipy_install_call,
        workspace_root    : Fb1Battlefieldgame.workspace_root,
        label             : 'fb1'
    ],
    'Kingston'  : [
        project_name      : Kingston,
        branch_folder     : 'dev',
        branch_name       : 'kin-dev',
        elipy_call        : Kingston.elipy_call,
        elipy_install_call: Kingston.elipy_install_call,
        workspace_root    : Kingston.workspace_root,
        label             : 'kin'
    ],
    'Granite'   : [
        project_name      : Granite,
        branch_folder     : 'gnt',
        branch_name       : 'gnt-proto',
        elipy_call        : Granite.elipy_call,
        workspace_root    : Granite.workspace_root,
        elipy_install_call: Granite.elipy_install_call,
        label             : 'gnt'
    ],
    'Merlin'    : [
        project_name      : Merlin,
        branch_folder     : 'game',
        branch_name       : 'build-main',
        elipy_call        : Merlin.elipy_call,
        workspace_root    : Merlin.workspace_root,
        elipy_install_call: Merlin.elipy_install_call,
        label             : 'mer'
    ],
    'NFSUpgrade': [
        project_name      : NFSUpgrade,
        branch_folder     : 'nfs',
        branch_name       : 'upgrade',
        elipy_call        : NFSUpgrade.elipy_call,
        workspace_root    : NFSUpgrade.workspace_root,
        elipy_install_call: NFSUpgrade.elipy_install_call,
        label             : 'nfs'
    ],
    'Bct'       : [
        project_name      : Bct,
        branch_folder     : 'mainline',
        branch_name       : 'trunk-code-dev',
        elipy_call        : Bct.elipy_call,
        workspace_root    : Bct.workspace_root,
        elipy_install_call: Bct.elipy_install_call,
        label             : 'bct'
    ],
]

build_deleter_projects.eachWithIndex { current_project, project_info, i ->
    def project = project_info.project_name.metaClass.properties.collectEntries { [it.name, project_info.project_name."${it.name}"] }
    project.p4_code_creds = project_info?.p4_code_creds ?: project.p4_code_creds
    project.elipy_call = project_info?.elipy_call ?: project.elipy_call
    project.p4_code_server = project_info?.p4_code_server ?: project.p4_code_server
    earo_deleter = project_info?.label == 'earo'
    project_info.putAll([
        elipy_call        : project.elipy_call,
        elipy_install_call: project.elipy_install_call,
        workspace_root    : project.workspace_root,
    ])
    def branch_info_sync = [
        code_branch  : project_info.branch_name,
        code_folder  : project_info.branch_folder,
        p4_code_creds: project.p4_code_creds,
    ]
    def start_name_prefix = "utility.build-deleter.${current_project}.start"

    def start_jobs = [
        (false): start_name_prefix,
        (true) : "${start_name_prefix}_with_delete-empty-folders",
    ]
    if (project_info.skip_empty_folders) {
        start_jobs = [(false): start_name_prefix]
    }
    start_jobs.each { delete_empty_folders, job_name ->
        LibBuildDeleter.start(
            pipelineJob(job_name) {
                //noinspection Indentation
                definition {
                    //noinspection Indentation
                    cps {
                        //noinspection Indentation
                        script(readFileFromWorkspace('src/scripts/schedulers/dicebuildjenkins/build_deleter_scheduler.groovy'))
                        //noinspection Indentation
                        sandbox(true)
                    }
                }
            },
            project,
            current_project,
            delete_empty_folders,
            i + 1,
            project_info
        )
    }
    def build_deleter_job = job('utility.build-deleter.' + current_project) {}
    LibBuildDeleter.job(build_deleter_job, project_info, current_project, project)
    if (!earo_deleter) {
        LibScm.sync_code(build_deleter_job, project, branch_info_sync)
    }
    LibJobDsl.initialP4revert(build_deleter_job, project, project_info, true, false)
    LibJobDsl.archive_non_build_logs(build_deleter_job)

    project.p4_code_servers.each { name, p4_port ->
        def clean_codestream = job("p4_clean_codestream.${name}." + current_project) {}
        LibMaintenance.p4_clean_codestream_job(clean_codestream, project_info.project_name, project_info, p4_port)
        LibScm.sync_code(clean_codestream, project, branch_info_sync)
        LibJobDsl.postclean_silverback(clean_codestream, project, project_info)
    }
}

outages_metrics_projects.each { current_project, project_info ->
    def project = project_info.project_name
    def branch_info_sync = [
        code_branch: project_info.branch_name,
        code_folder: project_info.branch_folder,
    ]

    def outage_metrics = job('register-outage-metrics-' + current_project) {
        description('Registers outage metrics for ' + current_project)
        label(project_info.label)
        logRotator(daysToKeep = 7, numToKeep = 50)
        quietPeriod(seconds = 0)
        customWorkspace(project_info.workspace_root)
        properties {
            disableConcurrentBuilds()
            disableResume()
        }
        parameters {
            stringParam {
                name('StartDate')
                defaultValue('')
                description('Start date, YYMMDD, for the period')
                trim(true)
            }
            stringParam {
                name('EndDate')
                defaultValue('')
                description('End date, YYMMDD, for the period')
                trim(true)
            }
            stringParam {
                name('MajorOutage')
                defaultValue('0')
                description('Hours of major outages')
                trim(true)
            }
            stringParam {
                name('SignificantOutage')
                defaultValue('0')
                description('Hours of significant outages')
                trim(true)
            }
        }
        wrappers {
            colorizeOutput()
            timestamps()
            buildName('${JOB_NAME}.${BUILD_NUMBER}')
            timeout {
                absolute(240)
                failBuild()
                writeDescription('Build failed due to timeout after {0} minutes')
            }
        }
        steps {
            batchFile(project_info.elipy_install_call)
            batchFile(project_info.elipy_call + ' register_outage_metric --end-date %EndDate% --start-date %StartDate% ' +
                ' --major-outage %MajorOutage% --significant-outage %SignificantOutage%')
        }
    }
    LibScm.sync_code(outage_metrics, project, branch_info_sync)
    LibJobDsl.initialP4revert(outage_metrics, project, project_info, true, false)
}

LibMaintenance.jenkinsShutdownJob(this)
