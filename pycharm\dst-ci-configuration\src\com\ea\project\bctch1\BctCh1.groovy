package com.ea.project.bctch1

import com.ea.project.Cobra

class BctCh1 {
    static String name = 'bctch1'
    static String short_name = 'bctch1'
    static Boolean frostbite_syncer_setup = true
    static Boolean single_perforce_server = true
    static Boolean presync_machines = false
    static String user_credentials = 'monkey.bct'
    static String vault_server_credentials = 'dice-online-cas-prod-secret-id'
    static String vault_server_variable = 'VAULT_ONLINE_CAS_PROD_SECRET_ID'
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static Boolean vault_win64_trial = false // TODO: Set to true when we start building win64-trial for most vaulted streams.
    static Boolean verify_post_vault = true
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = 'dice-online-gla-prod-secret-id'
    static String game_team_secrets_credential_online_prod = 'dice-online-gla-prod-secret-prod-id'
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'
    static List vault_default_platforms = [
        'linux64',
        'linuxserver',
        'ps5',
        'server',
        'win64',
        'xbsx',
    ]

    static String dataset = 'bfdata'
    static String frostbite_licensee = 'BattlefieldGame'

    static String webexport_script_path = 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = true
    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'DiceStockholm'
    static String elipy_scripts_config_file = 'elipy_bct.yml'

    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"
    static String elipy_call_earo = "${elipy_setup_call} && elipy --location earo"
    static String elipy_call_montreal = "${elipy_setup_call} && elipy --location Montreal"
    static String elipy_call_eala = "${elipy_setup_call} && elipy --location RippleEffect"
    static String elipy_call_task13 = "${elipy_setup_call} && elipy --location Task13"
    static String elipy_call_criterion = "${elipy_setup_call} && elipy --location Guildford"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"
    static String azure_elipy_call_eala = "$azure_elipy_setup_call && elipy --location RippleEffect"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '//bf'
    static String p4_code_creds = 'perforce-battlefield01'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_countername_prefix = 'bf'

    static String p4_data_root = p4_code_root
    static String p4_data_creds = p4_code_creds
    static String p4_data_server = p4_code_server
    static String p4_data_client = p4_code_client
    static String p4_data_client_env = p4_code_client_env

    static Map p4_code_servers = [
        'bct_build_criterion': 'oh-p4edge-fb.eu.ad.ea.com:2001',
        'bct_build_dice'     : p4_code_server,
        'bct_build_eala'     : 'dicela-p4edge-fb.la.ad.ea.com:2001',
    ]

    static List<Map> p4_data_servers = [
        [name: 'bct_build_criterion', p4_port: 'oh-p4edge-fb.eu.ad.ea.com:2001'],
        [name: 'bct_build_dice', p4_port: p4_data_server],
        [name: 'bct_build_eala', p4_port: 'dicela-p4edge-fb.la.ad.ea.com:2001'],
    ]

    static Map icepick_settings = [
        ignore_icepick_exit_code: false,
        icepick_test            : 'BFUnitTests',
        icepick_preflight_test  : 'BFUnitTests',
        settings_files          : 'Config/Icepick/IcepickSettings.ini',
    ]

    static String autotest_matrix = 'BctCh1AutotestMatrix'

    static Boolean compress_symbols = true
    static Boolean compress_symbols_code_win64server = false
    static Boolean compress_symbols_code_win64game = false
    static Boolean compress_symbols_code_xb1 = false
    static Boolean compress_symbols_code_xbsx = false

    static Boolean is_cloud = false
    static Boolean clean_master_version_check = true
    static Boolean expression_debug_data = true

    static Boolean shift_compression = true
    static Boolean file_hashes_frosty = true
}

